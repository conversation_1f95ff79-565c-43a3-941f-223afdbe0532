namespace Excalibur.Services;

public interface ITransactionManagerService
{
    bool IsWaitingForResult { get; }
    string? PendingTransactionId { get; }
    void RegisterPendingTransaction(string transactionId);
    void CompletePendingTransaction();
    bool CanExecuteNewTransaction();
    void QueueTransaction(DateTime transactionTime);
    bool HasQueuedTransactions();
    DateTime? GetNextQueuedTransaction();
    void ClearQueue();
}

public class TransactionManagerService : ITransactionManagerService
{
    private readonly Queue<DateTime> _pendingTransactions = new();
    private readonly object _lockObject = new();
    
    public bool IsWaitingForResult { get; private set; }
    public string? PendingTransactionId { get; private set; }

    public void RegisterPendingTransaction(string transactionId)
    {
        lock (_lockObject)
        {
            PendingTransactionId = transactionId;
            IsWaitingForResult = true;
        }
    }

    public void CompletePendingTransaction()
    {
        lock (_lockObject)
        {
            PendingTransactionId = null;
            IsWaitingForResult = false;
        }
    }

    public bool CanExecuteNewTransaction()
    {
        lock (_lockObject)
        {
            return !IsWaitingForResult;
        }
    }

    public void QueueTransaction(DateTime transactionTime)
    {
        lock (_lockObject)
        {
            _pendingTransactions.Enqueue(transactionTime);
        }
    }

    public bool HasQueuedTransactions()
    {
        lock (_lockObject)
        {
            return _pendingTransactions.Count > 0;
        }
    }

    public DateTime? GetNextQueuedTransaction()
    {
        lock (_lockObject)
        {
            return _pendingTransactions.Count > 0 ? _pendingTransactions.Dequeue() : null;
        }
    }

    public void ClearQueue()
    {
        lock (_lockObject)
        {
            _pendingTransactions.Clear();
        }
    }
}