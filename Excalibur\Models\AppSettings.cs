using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class ProposalSettings
{
    public string StakeText { get; set; } = "1.0";
    public string Duration { get; set; } = "5";
    public string DurationType { get; set; } = "t";
    public string BarrierText { get; set; } = "";
    public string Digit { get; set; } = "0";
    public bool IsContinuous { get; set; } = false;
}

public class MoneyManagementSettings
{
    public string FactorText { get; set; } = "2";
    public string LevelText { get; set; } = "1";
    public bool MartingaleEnabled { get; set; } = true;
    public string AmostraText { get; set; } = "5";
    public string WinText { get; set; } = "2";
    public string LossText { get; set; } = "3";
}

public class AppSettings
{
    public ProposalSettings? Proposal { get; set; }
    public MoneyManagementSettings? MoneyManagement { get; set; }
    public bool SaveProposalSettings { get; set; } = false;
    public bool SaveMoneyManagementSettings { get; set; } = false;
}
