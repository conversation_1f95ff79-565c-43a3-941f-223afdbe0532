namespace Excalibur.Services;

public interface IMartingaleService
{
    (decimal nextStake, bool shouldContinue) CalculateNextStake(decimal originalStake, bool isLoss, decimal factor, int maxLevel);
    void ResetMartingale();
    bool IsInMartingaleSequence { get; }
    int CurrentLevel { get; }
}

public class MartingaleService : IMartingaleService
{
    private int _currentLevel = 0;
    private bool _isInMartingaleSequence = false;
    private decimal _originalStake = 0;

    public bool IsInMartingaleSequence => _isInMartingaleSequence;
    public int CurrentLevel => _currentLevel;

    public (decimal nextStake, bool shouldContinue) CalculateNextStake(decimal originalStake, bool isLoss, decimal factor, int maxLevel)
    {
        if (isLoss)
        {
            if (!_isInMartingaleSequence)
            {
                // First loss - start martingale sequence
                if (maxLevel > 0)
                {
                    _currentLevel = 1;
                    _isInMartingaleSequence = true;
                    _originalStake = originalStake;

                    // Calculate: originalStake * factor^level
                    decimal nextStake = originalStake * (decimal)Math.Pow((double)factor, _currentLevel);
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Primeira perda - Nível {_currentLevel}: {originalStake} × {factor}^{_currentLevel} = {nextStake:F2}");
                    return (nextStake, true);
                }
                return (originalStake, true);
            }
            else
            {
                // Subsequent loss - calculate exponentially based on level
                if (_currentLevel < maxLevel)
                {
                    _currentLevel++;

                    // Calculate: originalStake * factor^level (exponential, not sequential)
                    decimal nextStake = _originalStake * (decimal)Math.Pow((double)factor, _currentLevel);
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Perda subsequente - Nível {_currentLevel}: {_originalStake} × {factor}^{_currentLevel} = {nextStake:F2}");
                    return (nextStake, true);
                }
                else
                {
                    // Max level reached - reset and use original stake
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Nível máximo {maxLevel} atingido - Resetando para stake original: {originalStake:F2}");
                    ResetMartingale();
                    return (originalStake, true);
                }
            }
        }
        else
        {
            // Win - reset to original stake
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Vitória - Resetando para stake original: {originalStake:F2}");
            ResetMartingale();
            return (originalStake, false);
        }
    }

    public void ResetMartingale()
    {
        _currentLevel = 0;
        _isInMartingaleSequence = false;
        _originalStake = 0;
        System.Diagnostics.Debug.WriteLine("[MARTINGALE] Estado resetado");
    }
}