[2025-07-17 20:48:50] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:48:50] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:48:50] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Carregando configurações salvas
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' alterado para: ATIVADO
[2025-07-17 20:48:50] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Configurações carregadas: Stake=0.35, Duration=2, DurationType=t
[2025-07-17 20:48:50] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:48:50] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Carregando configurações salvas
[2025-07-17 20:48:50] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Configurações carregadas: Factor=2.1, Level=8, Enabled=True
[2025-07-17 20:48:50] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:48:50] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:48:50] [Error] Excalibur.Services.SettingsService: [SETTINGS] Erro ao salvar configurações
[2025-07-17 20:48:54] [Information] Excalibur.Core.Services.DerivApiService: Conectado ao WebSocket da Deriv
[2025-07-17 20:48:54] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"authorize":"00I1wVoORm3kOYZ","req_id":2}
[2025-07-17 20:48:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"authorize":{"account_list":[{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR799153"},{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"BTC","currency_type":"crypto","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR917656"},{"account_category":"trading","account_type":"binary","broker":"VRTC","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":1,"landing_company_name":"virtual","linked_to":[],"loginid":"VRTC2033220"}],"balance":76289.69,"country":"br","currency":"USD","email":"<EMAIL>","fullname":"Mr Julio Cesar Ferreira Da Silva","is_virtual":1,"landing_company_fullname":"Deriv Limited","landing_company_name":"virtual","linked_to":[],"local_currencies":{"BRL":{"fractional_digits":2}},"loginid":"VRTC2033220","preferred_language":"EN","scopes":["read","trade","trading_information"],"upgradeable_landing_companies":["maltainvest","svg"],"user_id":6145887},"echo_req":{"authorize":"<not shown>","req_id":2},"msg_type":"authorize","req_id":2}
[2025-07-17 20:48:55] [Information] Excalibur.Core.Services.DerivApiService: Account info received: VRTC2033220, Balance: 76289.69 USD
[2025-07-17 20:48:55] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"active_symbols":"brief","req_id":3}
[2025-07-17 20:48:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"active_symbols":[{"allow_forward_starting":0,"close_only":0,"display_name":"AUD Basket","display_order":30,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDAUD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CAD","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CHF","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/JPY","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/NZD","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Australia 200","display_order":4,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_AS51","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"BTC\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryBTCUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":1,"close_only":0,"display_name":"Bear Market Index","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBEAR","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 300 Index","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 500 Index","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 600 Index","display_order":31,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 900 Index","display_order":36,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 1000 Index","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM1000","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Bull Market Index","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBULL","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 300 Index","display_order":28,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 500 Index","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 600 Index","display_order":32,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 900 Index","display_order":34,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 1000 Index","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH1000","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"ETH\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryETHUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":0,"close_only":0,"display_name":"EUR Basket","display_order":37,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDEUR","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/AUD","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CAD","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CHF","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/GBP","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURGBP","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/JPY","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/NZD","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxEURNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Euro 50","display_order":8,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SX5E","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"France 40","display_order":9,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FCHI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"GBP Basket","display_order":38,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDGBP","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/AUD","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CAD","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CHF","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/JPY","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NOK","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NZD","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/USD","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Germany 40","display_order":7,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_GDAXI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Gold Basket","display_order":33,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"commodity_basket","submarket_display_name":"Commodities Basket","symbol":"WLDXAU","symbol_type":"commodity_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"Gold\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAUUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Hong Kong 50","display_order":10,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_HSI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Japan 225","display_order":5,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_N225","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 10 Index","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 25 Index","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 50 Index","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 75 Index","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 100 Index","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD100","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/JPY","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/USD","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Netherlands 25","display_order":11,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_AEX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Palladium\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPDUSD","symbol_type":"commodities"},{"allow_forward_starting":0,"close_only":0,"display_name":"Platinum\/USD","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPTUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Silver\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAGUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 100","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 200","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG2","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 300","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG3","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 400","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG4","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 500","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG5","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Swiss 20","display_order":6,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SSMI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"UK 100","display_order":3,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FTSE","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US 500","display_order":0,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_SPC","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US Tech 100","display_order":1,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_NDX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"USD Basket","display_order":35,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDUSD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CAD","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CHF","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/JPY","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/MXN","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDMXN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/NOK","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/PLN","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDPLN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/SEK","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDSEK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 (1s) Index","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ10V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 Index","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 (1s) Index","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ25V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 Index","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 (1s) Index","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ50V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 Index","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 (1s) Index","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ75V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 Index","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 (1s) Index","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ100V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 Index","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_100","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 150 (1s) Index","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ150V","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 250 (1s) Index","display_order":29,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ250V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Wall Street 30","display_order":2,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_DJI","symbol_type":"stockindex"}],"echo_req":{"active_symbols":"brief","req_id":3},"msg_type":"active_symbols","req_id":3}
[2025-07-17 20:48:55] [Information] Excalibur.Core.Services.DerivApiService: Active symbols received: 85 symbols
[2025-07-17 20:48:55] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Clearing existing symbols and adding 85 new symbols
[2025-07-17 20:48:55] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Symbols collection populated with 85 items
[2025-07-17 20:48:59] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Carregando contratos para R_25
[2025-07-17 20:48:59] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"contracts_for":"R_25","req_id":4}
[2025-07-17 20:48:59] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"contracts_for":{"available":[{"barrier_category":"american","barriers":2,"contract_category":"accumulator","contract_category_display":"Accumulator","contract_display":"Accumulator Up","contract_type":"ACCU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","growth_rate_range":[0.01,0.02,0.03,0.04,0.05],"high_barrier":"+0.000","low_barrier":"+0.000","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Up","contract_type":"ASIANU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Down","contract_type":"ASIAND","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2907.825","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2907.825","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Matches","contract_type":"DIGITMATCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"match","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Differs","contract_type":"DIGITDIFF","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"differ","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Odd","contract_type":"DIGITODD","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"odd","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Even","contract_type":"DIGITEVEN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"even","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Over","contract_type":"DIGITOVER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"over","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Under","contract_type":"DIGITUNDER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"under","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2907.825","low_barrier":"2844.477","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2907.825","low_barrier":"2844.477","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"High Tick","contract_type":"TICKHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"high","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"Low Tick","contract_type":"TICKLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"low","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"Close-Low","contract_type":"LBFLOATCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Close","contract_type":"LBFLOATPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Low","contract_type":"LBHIGHLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"updown","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Up","contract_type":"MULTUP","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Down","contract_type":"MULTDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Ups","contract_type":"RUNHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Downs","contract_type":"RUNLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2907.825","low_barrier":"2844.477","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2907.825","low_barrier":"2844.477","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2907.825","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2907.825","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.181","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.181","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.46,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.46,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.46,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.46,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.46,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.910","barrier_category":"american","barrier_choices":["2.218","4.553","9.348","19.193","39.407","80.910","166.123","341.081","700.306","1437.866"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.46,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2880.000","barrier_category":"euro_non_atm","barrier_choices":["2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000","2920.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2880.000","barrier_category":"euro_non_atm","barrier_choices":["2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000","2920.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"}],"close":**********,"feed_license":"realtime","hit_count":72,"non_available":[],"open":**********,"spot":2875.73},"echo_req":{"contracts_for":"R_25","req_id":4},"msg_type":"contracts_for","req_id":4}
[2025-07-17 20:48:59] [Information] Excalibur.Core.Services.DerivApiService: Contracts received for R_25: 72 contracts
[2025-07-17 20:48:59] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contratos carregados para R_25: 72 contratos
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contrato selecionado: Higher para R_25
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Símbolo selecionado: R_25
[2025-07-17 20:49:03] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"ticks":"R_25","subscribe":1,"req_id":5}
[2025-07-17 20:49:03] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato selecionado: Higher (CALL)
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:03] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":6}
[2025-07-17 20:49:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Detalhes do contrato:
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractDisplay: Higher
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractTypeValue: CALL
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowBarrierField: False
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowDigitField: False
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel:   BarrierChoices: null
[2025-07-17 20:49:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":6,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.661"},"date_expiry":1752796546,"date_start":1752796542,"display_value":"0.35","id":"72ffb502-23e6-25db-58b5-7e7e9d056321","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.661,"spot_time":1752796542,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":6}
[2025-07-17 20:49:03] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:04] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":7}
[2025-07-17 20:49:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":7},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708}]},"req_id":7}
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática contínua
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Solicitando limpeza das tabelas Simulação e Compras
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' está: ATIVADO
[2025-07-17 20:49:05] [Information] Excalibur.Services.AppLoggerService: Fila de compras pendentes limpa
[2025-07-17 20:49:05] [Information] Excalibur.Services.AppLoggerService: Martingale resetado para valor inicial
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Timer de simulação automática iniciado. Primeira simulação será criada em breve.
[2025-07-17 20:49:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.474,"bid":2875.274,"epoch":1752796544,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.374,"symbol":"R_25"}}
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação automática será criada.
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação iniciará às 20:49:05.627
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:05.627, Expiração prevista: 20:49:09.627
[2025-07-17 20:49:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204905_7078, Duração=4s
[2025-07-17 20:49:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":8}
[2025-07-17 20:49:06] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":8,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.374"},"date_expiry":1752796549,"date_start":1752796545,"display_value":"0.35","id":"72ffb502-23e6-25db-58b5-7e7e9d056321","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.374,"spot_time":1752796544,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":8}
[2025-07-17 20:49:06] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:07] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.555,"bid":2875.355,"epoch":1752796546,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.455,"symbol":"R_25"}}
[2025-07-17 20:49:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.472,"bid":2875.272,"epoch":1752796548,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.372,"symbol":"R_25"}}
[2025-07-17 20:49:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:09] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:09] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:09] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":9}
[2025-07-17 20:49:09] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:49:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":9,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.372"},"date_expiry":1752796552,"date_start":1752796548,"display_value":"0.35","id":"72ffb502-23e6-25db-58b5-7e7e9d056321","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.372,"spot_time":1752796548,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":9}
[2025-07-17 20:49:09] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:09] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Aguardando período de segurança após expiração
[2025-07-17 20:49:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:09.627. Criando nova simulação às 20:49:10.628.
[2025-07-17 20:49:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:09.627 (expiração da anterior)
[2025-07-17 20:49:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:09.627, Expiração prevista: 20:49:13.627
[2025-07-17 20:49:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204909_3224, Duração=4s
[2025-07-17 20:49:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:11] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":10}
[2025-07-17 20:49:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.289,"bid":2875.089,"epoch":1752796550,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.189,"symbol":"R_25"}}
[2025-07-17 20:49:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":10,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.189"},"date_expiry":1752796554,"date_start":1752796550,"display_value":"0.35","id":"72ffb502-23e6-25db-58b5-7e7e9d056321","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.189,"spot_time":1752796550,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":10}
[2025-07-17 20:49:11] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:11] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=72ffb502-23e6-25db-58b5-7e7e9d056321, Price=0.66
[2025-07-17 20:49:11] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"72ffb502-23e6-25db-58b5-7e7e9d056321","price":0.66,"req_id":11}
[2025-07-17 20:49:11] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:49:11] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:49:12] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.34,"buy_price":0.35,"contract_id":288241253188,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796550,"shortcode":"CALL_R_25_0.66_1752796550_2T_S0P_0","start_time":1752796550,"transaction_id":574410325368},"echo_req":{"buy":"72ffb502-23e6-25db-58b5-7e7e9d056321","price":0.66,"req_id":11},"msg_type":"buy","req_id":11}
[2025-07-17 20:49:12] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241253188, BuyPrice=0.35, Payout=0.66, Balance=76289.34
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241253188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241253188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241253188
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241253188, ContractType=Higher
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:49:12] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":12}
[2025-07-17 20:49:12] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241253188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:12] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":12,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.189"},"date_expiry":1752796555,"date_start":1752796551,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.189,"spot_time":1752796550,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":12}
[2025-07-17 20:49:12] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:12] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":13}
[2025-07-17 20:49:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":13,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.189"},"date_expiry":1752796555,"date_start":1752796551,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.189,"spot_time":1752796550,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":13}
[2025-07-17 20:49:13] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.427,"bid":2875.227,"epoch":1752796552,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.327,"symbol":"R_25"}}
[2025-07-17 20:49:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:13.627. Criando nova simulação às 20:49:15.130.
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:13.627 (expiração da anterior)
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:13.627, Expiração prevista: 20:49:17.627
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204913_4344, Duração=4s
[2025-07-17 20:49:15] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.363,"bid":2875.163,"epoch":1752796554,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.263,"symbol":"R_25"}}
[2025-07-17 20:49:15] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:15] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":14}
[2025-07-17 20:49:15] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":14,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.263"},"date_expiry":1752796558,"date_start":1752796554,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.263,"spot_time":1752796554,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":14}
[2025-07-17 20:49:15] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:15] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241253188
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2875.189
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2875.263
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241253188
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241253188
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2875.189
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2875.263
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:49:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:16] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":15}
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:49:16] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:49:16] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":16}
[2025-07-17 20:49:16] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":15,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.263"},"date_expiry":1752796559,"date_start":1752796555,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.263,"spot_time":1752796554,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":15}
[2025-07-17 20:49:16] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":16},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708}]},"req_id":16}
[2025-07-17 20:49:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.354,"bid":2875.154,"epoch":1752796556,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.254,"symbol":"R_25"}}
[2025-07-17 20:49:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.617,"bid":2875.417,"epoch":1752796558,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.517,"symbol":"R_25"}}
[2025-07-17 20:49:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:19] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:19] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":17}
[2025-07-17 20:49:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:19] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":18}
[2025-07-17 20:49:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":17,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.517"},"date_expiry":1752796562,"date_start":1752796558,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.517,"spot_time":1752796558,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":17}
[2025-07-17 20:49:19] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:19] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":18},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248}]},"req_id":18}
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:17.627. Criando nova simulação às 20:49:20.123.
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:17.627 (expiração da anterior)
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:17.627, Expiração prevista: 20:49:21.627
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204917_1601, Duração=4s
[2025-07-17 20:49:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:20] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":19}
[2025-07-17 20:49:20] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":19,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.517"},"date_expiry":1752796563,"date_start":1752796559,"display_value":"0.35","id":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.517,"spot_time":1752796558,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":19}
[2025-07-17 20:49:20] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:20] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.462,"bid":2875.262,"epoch":1752796560,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.362,"symbol":"R_25"}}
[2025-07-17 20:49:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d, Price=0.66
[2025-07-17 20:49:21] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","price":0.66,"req_id":20}
[2025-07-17 20:49:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:49:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:49:22] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76288.99,"buy_price":0.35,"contract_id":288241264188,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796560,"shortcode":"CALL_R_25_0.66_1752796560_2T_S0P_0","start_time":1752796560,"transaction_id":574410346388},"echo_req":{"buy":"f192e6b2-f7ac-0c90-4e9f-77ec6eb5232d","price":0.66,"req_id":20},"msg_type":"buy","req_id":20}
[2025-07-17 20:49:22] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241264188, BuyPrice=0.35, Payout=0.66, Balance=76288.99
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241264188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241264188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241264188
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241264188, ContractType=Higher
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:49:22] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":21}
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241264188, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:22] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":21,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2875.362"},"date_expiry":1752796565,"date_start":1752796561,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2875.362,"spot_time":1752796560,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":21}
[2025-07-17 20:49:22] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":22}
[2025-07-17 20:49:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.079,"bid":2875.879,"epoch":1752796562,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.979,"symbol":"R_25"}}
[2025-07-17 20:49:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":22,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2875.362"},"date_expiry":1752796565,"date_start":1752796561,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2875.362,"spot_time":1752796560,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":22}
[2025-07-17 20:49:23] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:21.627. Criando nova simulação às 20:49:25.130.
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:21.627 (expiração da anterior)
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:21.627, Expiração prevista: 20:49:25.627
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204921_9206, Duração=4s
[2025-07-17 20:49:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.438,"bid":2876.238,"epoch":1752796564,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.338,"symbol":"R_25"}}
[2025-07-17 20:49:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:25] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":23}
[2025-07-17 20:49:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":23,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.338"},"date_expiry":1752796568,"date_start":1752796564,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.338,"spot_time":1752796564,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":23}
[2025-07-17 20:49:25] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:25] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:49:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241264188
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2875.362
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.338
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241264188
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241264188
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2875.362
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.338
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.74
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.74
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:49:26] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:26] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":24}
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.74 → 0.35
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:49:26] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:49:26] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":25}
[2025-07-17 20:49:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":24,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.338"},"date_expiry":1752796569,"date_start":1752796565,"display_value":"0.35","id":"b29c35a5-40e2-2356-6ab5-6d89fc363a2e","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.338,"spot_time":1752796564,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":24}
[2025-07-17 20:49:27] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:27] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":25},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248}]},"req_id":25}
[2025-07-17 20:49:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.135,"bid":2875.935,"epoch":1752796566,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.035,"symbol":"R_25"}}
[2025-07-17 20:49:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.038,"bid":2875.838,"epoch":1752796568,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.938,"symbol":"R_25"}}
[2025-07-17 20:49:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:29] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:29] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:29] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":26}
[2025-07-17 20:49:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":26,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.938"},"date_expiry":**********,"date_start":1752796568,"display_value":"0.35","id":"b29c35a5-40e2-2356-6ab5-6d89fc363a2e","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.938,"spot_time":1752796568,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":26}
[2025-07-17 20:49:29] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:29] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:25.627. Criando nova simulação às 20:49:30.132.
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:25.627 (expiração da anterior)
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:25.627, Expiração prevista: 20:49:29.627
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204925_3671, Duração=4s
[2025-07-17 20:49:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:30] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":27}
[2025-07-17 20:49:30] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":27,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.938"},"date_expiry":1752796573,"date_start":1752796569,"display_value":"0.35","id":"b29c35a5-40e2-2356-6ab5-6d89fc363a2e","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.938,"spot_time":1752796568,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":27}
[2025-07-17 20:49:30] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.036,"bid":2875.836,"epoch":1752796570,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.936,"symbol":"R_25"}}
[2025-07-17 20:49:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:31] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=b29c35a5-40e2-2356-6ab5-6d89fc363a2e, Price=0.66
[2025-07-17 20:49:31] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"b29c35a5-40e2-2356-6ab5-6d89fc363a2e","price":0.66,"req_id":28}
[2025-07-17 20:49:31] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:49:31] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:49:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:32] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.3,"buy_price":0.35,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"b29c35a5-40e2-2356-6ab5-6d89fc363a2e","price":0.66,"req_id":28},"msg_type":"buy","req_id":28}
[2025-07-17 20:49:32] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.35, Payout=0.66, Balance=76289.3
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:49:32] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:32] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":29}
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:32] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":29,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.936"},"date_expiry":**********,"date_start":**********,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.936,"spot_time":1752796570,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":29}
[2025-07-17 20:49:32] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:32] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:32] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":30}
[2025-07-17 20:49:33] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":30,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.936"},"date_expiry":**********,"date_start":**********,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.936,"spot_time":1752796570,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":30}
[2025-07-17 20:49:33] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:33] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:33] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.188,"bid":2875.988,"epoch":**********,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.088,"symbol":"R_25"}}
[2025-07-17 20:49:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":31}
[2025-07-17 20:49:34] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:49:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":32}
[2025-07-17 20:49:34] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":32},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier":"2876.088","barrier_count":1,"bid_price":0.31,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2876.088,"current_spot_display_value":"2876.088","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2876.088,"entry_spot_display_value":"2876.088","entry_tick":2876.088,"entry_tick_display_value":"2876.088","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.04,"profit_percentage":-11.43,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2876.088,"tick_display_value":"2876.088"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"},"req_id":32}
[2025-07-17 20:49:35] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier":"2876.088","barrier_count":1,"bid_price":0.31,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2876.088,"current_spot_display_value":"2876.088","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2876.088,"entry_spot_display_value":"2876.088","entry_tick":2876.088,"entry_tick_display_value":"2876.088","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.04,"profit_percentage":-11.43,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2876.088,"tick_display_value":"2876.088"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"}
[2025-07-17 20:49:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":31},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328}]},"req_id":31}
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:29.627. Criando nova simulação às 20:49:35.130.
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:29.627 (expiração da anterior)
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:29.627, Expiração prevista: 20:49:33.627
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204929_2639, Duração=4s
[2025-07-17 20:49:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.103,"bid":2875.903,"epoch":1752796574,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.003,"symbol":"R_25"}}
[2025-07-17 20:49:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:35] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":33}
[2025-07-17 20:49:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":33,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.003"},"date_expiry":1752796578,"date_start":1752796574,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.003,"spot_time":1752796574,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":33}
[2025-07-17 20:49:35] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2875.936
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.003
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2875.936
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.003
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:49:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:36] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":34}
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:49:36] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:49:36] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":35}
[2025-07-17 20:49:36] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":34,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.003"},"date_expiry":1752796579,"date_start":**********,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.003,"spot_time":1752796574,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":34}
[2025-07-17 20:49:36] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:36] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":35},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328}]},"req_id":35}
[2025-07-17 20:49:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:37] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.085,"bid":2875.885,"epoch":1752796576,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.985,"symbol":"R_25"}}
[2025-07-17 20:49:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:39] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.703,"bid":2875.503,"epoch":1752796578,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.603,"symbol":"R_25"}}
[2025-07-17 20:49:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:39] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:39] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:39] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":36}
[2025-07-17 20:49:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:39] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":36,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.603"},"date_expiry":1752796582,"date_start":1752796578,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.603,"spot_time":1752796578,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":36}
[2025-07-17 20:49:39] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:39] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:33.627. Criando nova simulação às 20:49:40.127.
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:33.627 (expiração da anterior)
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:33.627, Expiração prevista: 20:49:37.627
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204933_2694, Duração=4s
[2025-07-17 20:49:40] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:40] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":37}
[2025-07-17 20:49:40] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":37,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.603"},"date_expiry":1752796583,"date_start":1752796579,"display_value":"0.35","id":"b3746aca-d7bd-3092-8abe-3b9be9e79253","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.603,"spot_time":1752796578,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":37}
[2025-07-17 20:49:40] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:40] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:41] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.785,"bid":2875.585,"epoch":1752796580,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.685,"symbol":"R_25"}}
[2025-07-17 20:49:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:41] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=b3746aca-d7bd-3092-8abe-3b9be9e79253, Price=0.66
[2025-07-17 20:49:41] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"b3746aca-d7bd-3092-8abe-3b9be9e79253","price":0.66,"req_id":38}
[2025-07-17 20:49:41] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:49:41] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:49:42] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76288.95,"buy_price":0.35,"contract_id":288241284488,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796580,"shortcode":"CALL_R_25_0.66_1752796580_2T_S0P_0","start_time":1752796580,"transaction_id":574410386648},"echo_req":{"buy":"b3746aca-d7bd-3092-8abe-3b9be9e79253","price":0.66,"req_id":38},"msg_type":"buy","req_id":38}
[2025-07-17 20:49:42] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241284488, BuyPrice=0.35, Payout=0.66, Balance=76288.95
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241284488, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241284488, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241284488
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241284488, ContractType=Higher
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:49:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":39}
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241284488, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:42] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":39,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.685"},"date_expiry":1752796585,"date_start":1752796581,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.685,"spot_time":1752796580,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":39}
[2025-07-17 20:49:42] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":40}
[2025-07-17 20:49:42] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":40,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2875.685"},"date_expiry":1752796585,"date_start":1752796581,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2875.685,"spot_time":1752796580,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":40}
[2025-07-17 20:49:42] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2875.969,"bid":2875.769,"epoch":1752796582,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2875.869,"symbol":"R_25"}}
[2025-07-17 20:49:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:37.627. Criando nova simulação às 20:49:45.121.
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:37.627 (expiração da anterior)
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:37.627, Expiração prevista: 20:49:41.627
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204937_3001, Duração=4s
[2025-07-17 20:49:45] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.296,"bid":2876.096,"epoch":1752796584,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.196,"symbol":"R_25"}}
[2025-07-17 20:49:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:45] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":41}
[2025-07-17 20:49:45] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":41,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.196"},"date_expiry":1752796588,"date_start":1752796584,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.196,"spot_time":1752796584,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":41}
[2025-07-17 20:49:45] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:45] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241284488
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2875.685
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.196
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241284488
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241284488
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2875.685
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.196
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:49:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:46] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":42}
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:49:46] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:49:46] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":43}
[2025-07-17 20:49:46] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":42,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.196"},"date_expiry":1752796589,"date_start":1752796585,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.196,"spot_time":1752796584,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":42}
[2025-07-17 20:49:46] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:46] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":43},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108}]},"req_id":43}
[2025-07-17 20:49:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:47] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.491,"bid":2876.291,"epoch":1752796586,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.391,"symbol":"R_25"}}
[2025-07-17 20:49:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:49] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.654,"bid":2876.454,"epoch":1752796588,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.554,"symbol":"R_25"}}
[2025-07-17 20:49:49] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:49] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:49] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:49] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":44}
[2025-07-17 20:49:49] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:49:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":44,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.554"},"date_expiry":1752796592,"date_start":1752796588,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.554,"spot_time":1752796588,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":44}
[2025-07-17 20:49:49] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:49] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:50] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":45}
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:41.627. Criando nova simulação às 20:49:50.129.
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:41.627 (expiração da anterior)
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:41.627, Expiração prevista: 20:49:45.627
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204941_6348, Duração=4s
[2025-07-17 20:49:50] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":45},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948}]},"req_id":45}
[2025-07-17 20:49:50] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:50] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:50] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":46}
[2025-07-17 20:49:51] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":46,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.554"},"date_expiry":1752796594,"date_start":1752796590,"display_value":"0.35","id":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.554,"spot_time":1752796588,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":46}
[2025-07-17 20:49:51] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:51] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:51] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:51] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.608,"bid":2876.408,"epoch":1752796590,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.508,"symbol":"R_25"}}
[2025-07-17 20:49:51] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:51] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=c00f2e01-959c-ae1e-916b-7e7734bcbc1d, Price=0.66
[2025-07-17 20:49:51] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","price":0.66,"req_id":47}
[2025-07-17 20:49:51] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:49:51] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:49:52] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.26,"buy_price":0.35,"contract_id":288241293948,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796590,"shortcode":"CALL_R_25_0.66_1752796590_2T_S0P_0","start_time":1752796590,"transaction_id":574410405268},"echo_req":{"buy":"c00f2e01-959c-ae1e-916b-7e7734bcbc1d","price":0.66,"req_id":47},"msg_type":"buy","req_id":47}
[2025-07-17 20:49:52] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241293948, BuyPrice=0.35, Payout=0.66, Balance=76289.26
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241293948, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241293948, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241293948
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241293948, ContractType=Higher
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:49:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":48}
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241293948, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:49:52] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:52] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":48,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.508"},"date_expiry":1752796595,"date_start":1752796591,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.508,"spot_time":1752796590,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":48}
[2025-07-17 20:49:52] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:52] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":49}
[2025-07-17 20:49:52] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":49,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.508"},"date_expiry":1752796595,"date_start":1752796591,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.508,"spot_time":1752796590,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":49}
[2025-07-17 20:49:52] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:53] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.976,"bid":2876.776,"epoch":1752796592,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.876,"symbol":"R_25"}}
[2025-07-17 20:49:53] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:54] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:54] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:45.627. Criando nova simulação às 20:49:55.121.
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:45.627 (expiração da anterior)
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:45.627, Expiração prevista: 20:49:49.627
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204945_5224, Duração=4s
[2025-07-17 20:49:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2877.142,"bid":2876.942,"epoch":1752796594,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2877.042,"symbol":"R_25"}}
[2025-07-17 20:49:55] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:55] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":50}
[2025-07-17 20:49:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":50,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.042"},"date_expiry":1752796598,"date_start":1752796594,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.042,"spot_time":1752796594,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":50}
[2025-07-17 20:49:55] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:55] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:56] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:56] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241293948
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2876.508
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2877.042
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241293948
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241293948
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2876.508
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2877.042
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:49:56] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:56] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":51}
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:49:56] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:49:56] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":52}
[2025-07-17 20:49:56] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":52},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948}]},"req_id":52}
[2025-07-17 20:49:56] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":51,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.042"},"date_expiry":1752796599,"date_start":1752796595,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.042,"spot_time":1752796594,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":51}
[2025-07-17 20:49:56] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:56] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:49:57] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:57] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2877.059,"bid":2876.859,"epoch":1752796596,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.959,"symbol":"R_25"}}
[2025-07-17 20:49:57] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:58] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:58] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:59] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.977,"bid":2876.777,"epoch":1752796598,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.877,"symbol":"R_25"}}
[2025-07-17 20:49:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:49:59] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:49:59] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:49:59] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":53}
[2025-07-17 20:49:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:50:00] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":53,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.877"},"date_expiry":**********,"date_start":1752796598,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.877,"spot_time":1752796598,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":53}
[2025-07-17 20:50:00] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:49.627. Criando nova simulação às 20:50:00.124.
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:49.627 (expiração da anterior)
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:49.627, Expiração prevista: 20:49:53.627
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204949_2699, Duração=4s
[2025-07-17 20:50:00] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:00] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":54}
[2025-07-17 20:50:00] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":54,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.877"},"date_expiry":1752796603,"date_start":1752796599,"display_value":"0.35","id":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.877,"spot_time":1752796598,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":54}
[2025-07-17 20:50:00] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:00] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:01] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.847,"bid":2876.647,"epoch":**********,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.747,"symbol":"R_25"}}
[2025-07-17 20:50:01] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:01] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=5fa4251c-47d4-5d2a-4b26-feb6067bad5a, Price=0.66
[2025-07-17 20:50:01] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","price":0.66,"req_id":55}
[2025-07-17 20:50:01] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:50:01] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:50:02] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.57,"buy_price":0.35,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"5fa4251c-47d4-5d2a-4b26-feb6067bad5a","price":0.66,"req_id":55},"msg_type":"buy","req_id":55}
[2025-07-17 20:50:02] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.35, Payout=0.66, Balance=76289.57
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:50:02] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:02] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":56}
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:50:02] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:02] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":56,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.747"},"date_expiry":1752796605,"date_start":1752796601,"display_value":"0.35","id":"820540ab-ba8f-1fc2-f2e7-d8d3dbdd9ed7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.747,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":56}
[2025-07-17 20:50:02] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:02] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:02] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:02] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":57}
[2025-07-17 20:50:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":57,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.747"},"date_expiry":1752796605,"date_start":1752796601,"display_value":"0.35","id":"820540ab-ba8f-1fc2-f2e7-d8d3dbdd9ed7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.747,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":57}
[2025-07-17 20:50:03] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.807,"bid":2876.607,"epoch":**********,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.707,"symbol":"R_25"}}
[2025-07-17 20:50:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:04] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:04] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":58}
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":59}
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:53.627. Criando nova simulação às 20:50:05.130.
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:53.627 (expiração da anterior)
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:53.627, Expiração prevista: 20:49:57.627
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204953_2473, Duração=4s
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.7,"bid":2876.5,"epoch":**********,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.6,"symbol":"R_25"}}
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":59},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier":"2876.707","barrier_count":1,"bid_price":0.2,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2876.6,"current_spot_display_value":"2876.600","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2876.707,"entry_spot_display_value":"2876.707","entry_tick":2876.707,"entry_tick_display_value":"2876.707","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.15,"profit_percentage":-42.86,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2876.707,"tick_display_value":"2876.707"},{"epoch":**********,"tick":2876.6,"tick_display_value":"2876.600"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"},"req_id":59}
[2025-07-17 20:50:05] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier":"2876.707","barrier_count":1,"bid_price":0.2,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2876.6,"current_spot_display_value":"2876.600","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2876.707,"entry_spot_display_value":"2876.707","entry_tick":2876.707,"entry_tick_display_value":"2876.707","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.15,"profit_percentage":-42.86,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2876.707,"tick_display_value":"2876.707"},{"epoch":**********,"tick":2876.6,"tick_display_value":"2876.600"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"}
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":58},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048}]},"req_id":58}
[2025-07-17 20:50:05] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":60}
[2025-07-17 20:50:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":60,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.600"},"date_expiry":1752796608,"date_start":**********,"display_value":"0.35","id":"820540ab-ba8f-1fc2-f2e7-d8d3dbdd9ed7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.6,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":60}
[2025-07-17 20:50:05] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:05] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:50:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2876.747
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.600
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: -0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: PERDA
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2876.747
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.600
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: -0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: PERDA
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: True
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.74
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: True
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:50:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":61}
[2025-07-17 20:50:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:50:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":62}
[2025-07-17 20:50:06] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":61,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.600"},"date_expiry":1752796609,"date_start":1752796605,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.6,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":61}
[2025-07-17 20:50:06] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:06] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":62},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048}]},"req_id":62}
[2025-07-17 20:50:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:07] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.423,"bid":2876.223,"epoch":1752796606,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.323,"symbol":"R_25"}}
[2025-07-17 20:50:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.443,"bid":2876.243,"epoch":1752796608,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.343,"symbol":"R_25"}}
[2025-07-17 20:50:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:09] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:50:09] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:09] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":63}
[2025-07-17 20:50:09] [Information] Excalibur.Services.AppLoggerService: Martingale está ativo na tabela Compras (nível 1), adicionando à fila
[2025-07-17 20:50:09] [Information] Excalibur.Services.AppLoggerService: Compra adicionada à fila. Total na fila: sim
[2025-07-17 20:50:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:50:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":63,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.343"},"date_expiry":1752796612,"date_start":1752796608,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.343,"spot_time":1752796608,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":63}
[2025-07-17 20:50:09] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:09] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:49:57.627. Criando nova simulação às 20:50:10.134.
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:49:57.627 (expiração da anterior)
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:49:57.627, Expiração prevista: 20:50:01.627
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204957_2370, Duração=4s
[2025-07-17 20:50:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:10] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":64}
[2025-07-17 20:50:10] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":64,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.343"},"date_expiry":1752796613,"date_start":1752796609,"display_value":"0.74","id":"57071cc7-d778-8322-f480-5946b2048492","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.343,"spot_time":1752796608,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":64}
[2025-07-17 20:50:10] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.401,"bid":2876.201,"epoch":1752796610,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.301,"symbol":"R_25"}}
[2025-07-17 20:50:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:11] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=57071cc7-d778-8322-f480-5946b2048492, Price=1.44
[2025-07-17 20:50:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"57071cc7-d778-8322-f480-5946b2048492","price":1.44,"req_id":65}
[2025-07-17 20:50:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:50:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:50:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76288.83,"buy_price":0.74,"contract_id":288241313408,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"purchase_time":1752796612,"shortcode":"CALL_R_25_1.44_1752796612_2T_S0P_0","start_time":1752796612,"transaction_id":574410443468},"echo_req":{"buy":"57071cc7-d778-8322-f480-5946b2048492","price":1.44,"req_id":65},"msg_type":"buy","req_id":65}
[2025-07-17 20:50:13] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241313408, BuyPrice=0.74, Payout=1.44, Balance=76288.83
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241313408, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241313408, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241313408
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241313408, ContractType=Higher
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:50:13] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:13] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":66}
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241313408, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:50:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.127,"bid":2875.927,"epoch":1752796612,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.027,"symbol":"R_25"}}
[2025-07-17 20:50:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":66,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.027"},"date_expiry":1752796616,"date_start":1752796612,"display_value":"0.74","id":"84bd87c3-9fe0-59ad-6014-2c42d9acdf07","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.027,"spot_time":1752796612,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":66}
[2025-07-17 20:50:13] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:13] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":67}
[2025-07-17 20:50:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:14] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":67,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.027"},"date_expiry":1752796617,"date_start":1752796613,"display_value":"0.74","id":"84bd87c3-9fe0-59ad-6014-2c42d9acdf07","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.027,"spot_time":1752796612,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":67}
[2025-07-17 20:50:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:50:01.627. Criando nova simulação às 20:50:15.132.
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:50:01.627 (expiração da anterior)
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:50:01.627. Criando nova simulação às 20:50:16.124.
[2025-07-17 20:50:16] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:50:01.627. Criando nova simulação às 20:50:15.625.
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:50:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, cancelando operação duplicada
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, cancelando operação duplicada
[2025-07-17 20:50:16] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.182,"bid":2875.982,"epoch":1752796614,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.082,"symbol":"R_25"}}
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:50:01.627, Expiração prevista: 20:50:05.627
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205001_3366, Duração=4s
[2025-07-17 20:50:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:16] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":68}
[2025-07-17 20:50:16] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":68,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2876.082"},"date_expiry":1752796619,"date_start":1752796615,"display_value":"0.74","id":"84bd87c3-9fe0-59ad-6014-2c42d9acdf07","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2876.082,"spot_time":1752796614,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":68}
[2025-07-17 20:50:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241313408
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2876.301
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.082
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: -0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: PERDA
[2025-07-17 20:50:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:19] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241313408
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:50:19] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241313408
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.685,"bid":2876.485,"epoch":1752796616,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.585,"symbol":"R_25"}}
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2876.301
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.082
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: -0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: PERDA
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.74
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: True
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 1.54
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: True
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 2
[2025-07-17 20:50:19] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=1.54, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":1.54,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":69}
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: Processando fila de compras pendentes
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: Executando compra pendente da fila (criada em: 20:50:09)
[2025-07-17 20:50:19] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":70}
[2025-07-17 20:50:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:50:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2876.728,"bid":2876.528,"epoch":1752796618,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.628,"symbol":"R_25"}}
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":1.54,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":69,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":1.54,"contract_details":{"barrier":"2876.628"},"date_expiry":1752796622,"date_start":1752796618,"display_value":"1.54","id":"df87eaa9-5090-f33f-1baf-325d7dfc4d84","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":3.01,"spot":2876.628,"spot_time":1752796618,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":69}
[2025-07-17 20:50:19] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=3.01, AskPrice=1.54
[2025-07-17 20:50:19] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=3.01, AskPrice=1.54
[2025-07-17 20:50:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":70},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796606,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748}]},"req_id":70}
[2025-07-17 20:50:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:20] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":71}
[2025-07-17 20:50:20] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":71},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796606,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":1752796576,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748}]},"req_id":71}
[2025-07-17 20:50:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=df87eaa9-5090-f33f-1baf-325d7dfc4d84, Price=3.01
[2025-07-17 20:50:21] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"df87eaa9-5090-f33f-1baf-325d7dfc4d84","price":3.01,"req_id":72}
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:50:05.627. Criando nova simulação às 20:50:21.130.
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:50:05.627 (expiração da anterior)
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=1.54, Duration=2t (4s)
[2025-07-17 20:50:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:50:05.627, Expiração prevista: 20:50:09.627
[2025-07-17 20:50:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524"},"tick":{"ask":2877.053,"bid":2876.853,"epoch":1752796620,"id":"b3e4d1ce-765d-00df-75ca-b5adf27ef524","pip_size":3,"quote":2876.953,"symbol":"R_25"}}
[2025-07-17 20:50:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=1.54, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:50:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":1.54,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":73}
[2025-07-17 20:50:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:22] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:50:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:35] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":74}
[2025-07-17 20:50:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:40] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
[2025-07-17 20:50:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Já está criando uma simulação, aguardando...
