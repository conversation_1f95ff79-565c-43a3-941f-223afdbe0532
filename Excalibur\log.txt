[2025-07-17 20:57:54] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:57:54] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:57:54] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Carregando configurações salvas
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' alterado para: ATIVADO
[2025-07-17 20:57:54] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Configurações carregadas: Stake=0.35, Duration=2, DurationType=t
[2025-07-17 20:57:54] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:57:54] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Carregando configurações salvas
[2025-07-17 20:57:54] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Configurações carregadas: Factor=2.1, Level=8, Enabled=True
[2025-07-17 20:57:54] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:57:54] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:57:54] [Error] Excalibur.Services.SettingsService: [SETTINGS] Erro ao salvar configurações
[2025-07-17 20:57:56] [Information] Excalibur.Core.Services.DerivApiService: Conectado ao WebSocket da Deriv
[2025-07-17 20:57:56] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"authorize":"00I1wVoORm3kOYZ","req_id":2}
[2025-07-17 20:57:56] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"authorize":{"account_list":[{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR799153"},{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"BTC","currency_type":"crypto","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR917656"},{"account_category":"trading","account_type":"binary","broker":"VRTC","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":1,"landing_company_name":"virtual","linked_to":[],"loginid":"VRTC2033220"}],"balance":76291.74,"country":"br","currency":"USD","email":"<EMAIL>","fullname":"Mr Julio Cesar Ferreira Da Silva","is_virtual":1,"landing_company_fullname":"Deriv Limited","landing_company_name":"virtual","linked_to":[],"local_currencies":{"BRL":{"fractional_digits":2}},"loginid":"VRTC2033220","preferred_language":"EN","scopes":["read","trade","trading_information"],"upgradeable_landing_companies":["maltainvest","svg"],"user_id":6145887},"echo_req":{"authorize":"<not shown>","req_id":2},"msg_type":"authorize","req_id":2}
[2025-07-17 20:57:56] [Information] Excalibur.Core.Services.DerivApiService: Account info received: VRTC2033220, Balance: 76291.74 USD
[2025-07-17 20:57:56] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"active_symbols":"brief","req_id":3}
[2025-07-17 20:57:57] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"active_symbols":[{"allow_forward_starting":0,"close_only":0,"display_name":"AUD Basket","display_order":30,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDAUD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CAD","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CHF","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/JPY","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/NZD","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Australia 200","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_AS51","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"BTC\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryBTCUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":1,"close_only":0,"display_name":"Bear Market Index","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBEAR","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 300 Index","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 500 Index","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 600 Index","display_order":31,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 900 Index","display_order":36,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 1000 Index","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM1000","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Bull Market Index","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBULL","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 300 Index","display_order":28,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 500 Index","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 600 Index","display_order":32,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 900 Index","display_order":34,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 1000 Index","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH1000","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"ETH\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryETHUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":0,"close_only":0,"display_name":"EUR Basket","display_order":37,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDEUR","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/AUD","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CAD","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CHF","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/GBP","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURGBP","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/JPY","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/NZD","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxEURNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Euro 50","display_order":8,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SX5E","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"France 40","display_order":9,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FCHI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"GBP Basket","display_order":38,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDGBP","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/AUD","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CAD","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CHF","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/JPY","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NOK","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NZD","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/USD","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Germany 40","display_order":7,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_GDAXI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Gold Basket","display_order":33,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"commodity_basket","submarket_display_name":"Commodities Basket","symbol":"WLDXAU","symbol_type":"commodity_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"Gold\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAUUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Hong Kong 50","display_order":10,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_HSI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Japan 225","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_N225","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 10 Index","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 25 Index","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 50 Index","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 75 Index","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 100 Index","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD100","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/JPY","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/USD","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Netherlands 25","display_order":11,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_AEX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Palladium\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPDUSD","symbol_type":"commodities"},{"allow_forward_starting":0,"close_only":0,"display_name":"Platinum\/USD","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPTUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Silver\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAGUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 100","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 200","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG2","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 300","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG3","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 400","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG4","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 500","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG5","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Swiss 20","display_order":6,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SSMI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"UK 100","display_order":3,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FTSE","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US 500","display_order":0,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_SPC","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US Tech 100","display_order":1,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_NDX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"USD Basket","display_order":35,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDUSD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CAD","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CHF","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/JPY","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/MXN","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDMXN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/NOK","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/PLN","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDPLN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/SEK","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDSEK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 (1s) Index","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ10V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 Index","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 (1s) Index","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ25V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 Index","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 (1s) Index","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ50V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 Index","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 (1s) Index","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ75V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 Index","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 (1s) Index","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ100V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 Index","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_100","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 150 (1s) Index","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ150V","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 250 (1s) Index","display_order":29,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ250V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Wall Street 30","display_order":2,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_DJI","symbol_type":"stockindex"}],"echo_req":{"active_symbols":"brief","req_id":3},"msg_type":"active_symbols","req_id":3}
[2025-07-17 20:57:57] [Information] Excalibur.Core.Services.DerivApiService: Active symbols received: 85 symbols
[2025-07-17 20:57:57] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Clearing existing symbols and adding 85 new symbols
[2025-07-17 20:57:57] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Symbols collection populated with 85 items
[2025-07-17 20:58:01] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Carregando contratos para R_25
[2025-07-17 20:58:01] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"contracts_for":"R_25","req_id":4}
[2025-07-17 20:58:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"contracts_for":{"available":[{"barrier_category":"american","barriers":2,"contract_category":"accumulator","contract_category_display":"Accumulator","contract_display":"Accumulator Up","contract_type":"ACCU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","growth_rate_range":[0.01,0.02,0.03,0.04,0.05],"high_barrier":"+0.000","low_barrier":"+0.000","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Up","contract_type":"ASIANU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Down","contract_type":"ASIAND","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2905.300","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2905.300","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752969599","date":"1752883200","open":"1752883200"},{"close":"1753055999","date":"1752969600","open":"1752969600"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752969599","date":"1752883200","open":"1752883200"},{"close":"1753055999","date":"1752969600","open":"1752969600"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752969599","date":"1752883200","open":"1752883200"},{"close":"1753055999","date":"1752969600","open":"1752969600"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752969599","date":"1752883200","open":"1752883200"},{"close":"1753055999","date":"1752969600","open":"1752969600"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Matches","contract_type":"DIGITMATCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"match","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Differs","contract_type":"DIGITDIFF","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"differ","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Odd","contract_type":"DIGITODD","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"odd","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Even","contract_type":"DIGITEVEN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"even","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Over","contract_type":"DIGITOVER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"over","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Under","contract_type":"DIGITUNDER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"under","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2905.300","low_barrier":"2842.007","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2905.300","low_barrier":"2842.007","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.180","low_barrier":"-1.179","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.180","low_barrier":"-1.179","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"High Tick","contract_type":"TICKHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"high","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"Low Tick","contract_type":"TICKLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"low","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"Close-Low","contract_type":"LBFLOATCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Close","contract_type":"LBFLOATPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Low","contract_type":"LBHIGHLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"updown","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Up","contract_type":"MULTUP","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Down","contract_type":"MULTDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Ups","contract_type":"RUNHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Downs","contract_type":"RUNLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2905.300","low_barrier":"2842.007","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2905.300","low_barrier":"2842.007","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.180","low_barrier":"-1.179","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.180","low_barrier":"-1.179","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2905.300","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2905.300","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.180","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.180","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.21,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.21,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.21,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.21,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.21,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.839","barrier_category":"american","barrier_choices":["2.216","4.549","9.340","19.177","39.373","80.839","165.978","340.785","699.698","1436.617"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.21,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2870.000","barrier_category":"euro_non_atm","barrier_choices":["2830.000","2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2870.000","barrier_category":"euro_non_atm","barrier_choices":["2830.000","2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"}],"close":**********,"feed_license":"realtime","hit_count":72,"non_available":[],"open":**********,"spot":2873.233},"echo_req":{"contracts_for":"R_25","req_id":4},"msg_type":"contracts_for","req_id":4}
[2025-07-17 20:58:01] [Information] Excalibur.Core.Services.DerivApiService: Contracts received for R_25: 72 contracts
[2025-07-17 20:58:01] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contratos carregados para R_25: 72 contratos
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contrato selecionado: Higher para R_25
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Símbolo selecionado: R_25
[2025-07-17 20:58:03] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"ticks":"R_25","subscribe":1,"req_id":5}
[2025-07-17 20:58:03] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato selecionado: Higher (CALL)
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:03] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":6}
[2025-07-17 20:58:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Detalhes do contrato:
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractDisplay: Higher
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractTypeValue: CALL
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowBarrierField: False
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowDigitField: False
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel:   BarrierChoices: null
[2025-07-17 20:58:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":6,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2873.043"},"date_expiry":1752797086,"date_start":1752797082,"display_value":"0.35","id":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2873.043,"spot_time":1752797082,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":6}
[2025-07-17 20:58:03] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.269,"bid":2873.069,"epoch":1752797084,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.169,"symbol":"R_25"}}
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática contínua
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Solicitando limpeza das tabelas Simulação e Compras
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' está: ATIVADO
[2025-07-17 20:58:05] [Information] Excalibur.Services.AppLoggerService: Fila de compras pendentes limpa
[2025-07-17 20:58:05] [Information] Excalibur.Services.AppLoggerService: Martingale resetado para valor inicial
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Timer de simulação automática iniciado. Primeira simulação será criada em breve.
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação automática será criada.
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação iniciará às 20:58:05.873
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:05.873, Expiração prevista: 20:58:09.873
[2025-07-17 20:58:05] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205805_4020, Duração=4s
[2025-07-17 20:58:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":7}
[2025-07-17 20:58:06] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":7,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2873.169"},"date_expiry":1752797089,"date_start":1752797085,"display_value":"0.35","id":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2873.169,"spot_time":1752797084,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":7}
[2025-07-17 20:58:06] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:07] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.005,"bid":2872.805,"epoch":1752797086,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.905,"symbol":"R_25"}}
[2025-07-17 20:58:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:08] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":8}
[2025-07-17 20:58:08] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":8},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088}]},"req_id":8}
[2025-07-17 20:58:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.702,"bid":2872.502,"epoch":1752797088,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.602,"symbol":"R_25"}}
[2025-07-17 20:58:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:10] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:10] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":9}
[2025-07-17 20:58:10] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:58:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:10] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":9,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.602"},"date_expiry":1752797093,"date_start":1752797089,"display_value":"0.35","id":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.602,"spot_time":1752797088,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":9}
[2025-07-17 20:58:10] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:09.873. Criando nova simulação às 20:58:10.879.
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:09.873 (expiração da anterior)
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:09.873, Expiração prevista: 20:58:13.873
[2025-07-17 20:58:10] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205809_4796, Duração=4s
[2025-07-17 20:58:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.624,"bid":2872.424,"epoch":1752797090,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.524,"symbol":"R_25"}}
[2025-07-17 20:58:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:11] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":10}
[2025-07-17 20:58:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":10,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.524"},"date_expiry":1752797094,"date_start":1752797090,"display_value":"0.35","id":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.524,"spot_time":1752797090,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":10}
[2025-07-17 20:58:11] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8, Price=0.66
[2025-07-17 20:58:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","price":0.66,"req_id":11}
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:58:12] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76291.39,"buy_price":0.35,"contract_id":288241773868,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752797091,"shortcode":"CALL_R_25_0.66_1752797091_2T_S0P_0","start_time":1752797091,"transaction_id":574411365248},"echo_req":{"buy":"75d48cd4-e3be-f5ba-a6cd-e5e64f25d4e8","price":0.66,"req_id":11},"msg_type":"buy","req_id":11}
[2025-07-17 20:58:12] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241773868, BuyPrice=0.35, Payout=0.66, Balance=76291.39
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241773868, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241773868, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241773868
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241773868, ContractType=Higher
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:58:12] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":12}
[2025-07-17 20:58:12] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241773868, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:12] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":12,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.524"},"date_expiry":1752797095,"date_start":1752797091,"display_value":"0.35","id":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.524,"spot_time":1752797090,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":12}
[2025-07-17 20:58:12] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:12] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.733,"bid":2872.533,"epoch":1752797092,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.633,"symbol":"R_25"}}
[2025-07-17 20:58:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:13] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":13}
[2025-07-17 20:58:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":13,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.633"},"date_expiry":1752797096,"date_start":1752797092,"display_value":"0.35","id":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.633,"spot_time":1752797092,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":13}
[2025-07-17 20:58:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:13] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:15] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.55,"bid":2872.35,"epoch":1752797094,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.45,"symbol":"R_25"}}
[2025-07-17 20:58:15] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:13.873. Criando nova simulação às 20:58:15.868.
[2025-07-17 20:58:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:13.873 (expiração da anterior)
[2025-07-17 20:58:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:13.873, Expiração prevista: 20:58:17.873
[2025-07-17 20:58:15] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205813_4572, Duração=4s
[2025-07-17 20:58:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:16] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":14}
[2025-07-17 20:58:16] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":14,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.450"},"date_expiry":1752797099,"date_start":1752797095,"display_value":"0.35","id":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.45,"spot_time":1752797094,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":14}
[2025-07-17 20:58:16] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.592,"bid":2872.392,"epoch":1752797096,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.492,"symbol":"R_25"}}
[2025-07-17 20:58:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241773868
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2872.524
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2872.492
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: -0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: PERDA
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241773868
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241773868
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2872.524
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2872.492
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: -0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: PERDA
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: True
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.74
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: True
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:58:17] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:17] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":15}
[2025-07-17 20:58:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:58:17] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":16}
[2025-07-17 20:58:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":15,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2872.492"},"date_expiry":1752797100,"date_start":1752797096,"display_value":"0.74","id":"4a10f2d2-3ba6-607a-adf4-a5460e7814ea","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2872.492,"spot_time":1752797096,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":15}
[2025-07-17 20:58:17] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:17] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":16},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108}]},"req_id":16}
[2025-07-17 20:58:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.135,"bid":2871.935,"epoch":1752797098,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.035,"symbol":"R_25"}}
[2025-07-17 20:58:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:20] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:20] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":17}
[2025-07-17 20:58:20] [Information] Excalibur.Services.AppLoggerService: Martingale está ativo na tabela Compras (nível 1), adicionando à fila
[2025-07-17 20:58:20] [Information] Excalibur.Services.AppLoggerService: Compra adicionada à fila. Total na fila: sim
[2025-07-17 20:58:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:20] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":17,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2872.035"},"date_expiry":1752797103,"date_start":1752797099,"display_value":"0.74","id":"4a10f2d2-3ba6-607a-adf4-a5460e7814ea","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2872.035,"spot_time":1752797098,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":17}
[2025-07-17 20:58:20] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:17.873. Criando nova simulação às 20:58:20.865.
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:17.873 (expiração da anterior)
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:17.873, Expiração prevista: 20:58:21.873
[2025-07-17 20:58:20] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205817_5205, Duração=4s
[2025-07-17 20:58:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:21] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:21] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":18}
[2025-07-17 20:58:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2871.787,"bid":2871.587,"epoch":1752797100,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.687,"symbol":"R_25"}}
[2025-07-17 20:58:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":18,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2871.687"},"date_expiry":1752797104,"date_start":1752797100,"display_value":"0.74","id":"4a10f2d2-3ba6-607a-adf4-a5460e7814ea","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2871.687,"spot_time":1752797100,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":18}
[2025-07-17 20:58:21] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:21] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=4a10f2d2-3ba6-607a-adf4-a5460e7814ea, Price=1.44
[2025-07-17 20:58:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"4a10f2d2-3ba6-607a-adf4-a5460e7814ea","price":1.44,"req_id":19}
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:58:22] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76290.65,"buy_price":0.74,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"4a10f2d2-3ba6-607a-adf4-a5460e7814ea","price":1.44,"req_id":19},"msg_type":"buy","req_id":19}
[2025-07-17 20:58:22] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.74, Payout=1.44, Balance=76290.65
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:58:22] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":20}
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:58:22] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":20,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2871.687"},"date_expiry":**********,"date_start":**********,"display_value":"0.74","id":"7f94b33a-405f-fdcf-34d1-22b3263f3f49","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2871.687,"spot_time":1752797100,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":20}
[2025-07-17 20:58:22] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:22] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2871.842,"bid":2871.642,"epoch":**********,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.742,"symbol":"R_25"}}
[2025-07-17 20:58:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":21}
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":22}
[2025-07-17 20:58:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":23}
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":22},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier":"2871.742","barrier_count":1,"bid_price":0.7,"buy_price":0.74,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2871.742,"current_spot_display_value":"2871.742","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2871.742,"entry_spot_display_value":"2871.742","entry_tick":2871.742,"entry_tick_display_value":"2871.742","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"profit":-0.04,"profit_percentage":-5.41,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2871.742,"tick_display_value":"2871.742"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"},"req_id":22}
[2025-07-17 20:58:23] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier":"2871.742","barrier_count":1,"bid_price":0.7,"buy_price":0.74,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2871.742,"current_spot_display_value":"2871.742","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2871.742,"entry_spot_display_value":"2871.742","entry_tick":2871.742,"entry_tick_display_value":"2871.742","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"profit":-0.04,"profit_percentage":-5.41,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2871.742,"tick_display_value":"2871.742"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"}
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":21},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108}]},"req_id":21}
[2025-07-17 20:58:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":23,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2871.742"},"date_expiry":1752797106,"date_start":**********,"display_value":"0.74","id":"7f94b33a-405f-fdcf-34d1-22b3263f3f49","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2871.742,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":23}
[2025-07-17 20:58:23] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2871.725,"bid":2871.525,"epoch":1752797104,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.625,"symbol":"R_25"}}
[2025-07-17 20:58:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:21.873. Criando nova simulação às 20:58:25.875.
[2025-07-17 20:58:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:21.873 (expiração da anterior)
[2025-07-17 20:58:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:58:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:21.873, Expiração prevista: 20:58:25.873
[2025-07-17 20:58:25] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205821_4461, Duração=4s
[2025-07-17 20:58:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:26] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:26] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":24}
[2025-07-17 20:58:26] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":24,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2871.625"},"date_expiry":1752797109,"date_start":**********,"display_value":"0.74","id":"7f94b33a-405f-fdcf-34d1-22b3263f3f49","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2871.625,"spot_time":1752797104,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":24}
[2025-07-17 20:58:26] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:26] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:58:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.067,"bid":2871.867,"epoch":1752797106,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.967,"symbol":"R_25"}}
[2025-07-17 20:58:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2871.687
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2871.967
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.74
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.70
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2871.687
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2871.967
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.74
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.70
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.74
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.74
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:58:27] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:27] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":25}
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.74 → 0.35
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: Processando fila de compras pendentes
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: Executando compra pendente da fila (criada em: 20:58:20)
[2025-07-17 20:58:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:58:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:27] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":26}
[2025-07-17 20:58:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":25,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2871.967"},"date_expiry":1752797110,"date_start":1752797106,"display_value":"0.35","id":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2871.967,"spot_time":1752797106,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":25}
[2025-07-17 20:58:27] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:27] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":26},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308}]},"req_id":26}
[2025-07-17 20:58:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.043,"bid":2871.843,"epoch":1752797108,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.943,"symbol":"R_25"}}
[2025-07-17 20:58:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=d5c283b8-4ec5-1746-f5ed-8735ae4e1f37, Price=0.66
[2025-07-17 20:58:29] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","price":0.66,"req_id":27}
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:58:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76291.74,"buy_price":0.35,"contract_id":288241790648,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752797108,"shortcode":"CALL_R_25_0.66_1752797108_2T_S0P_0","start_time":1752797108,"transaction_id":574411398488},"echo_req":{"buy":"d5c283b8-4ec5-1746-f5ed-8735ae4e1f37","price":0.66,"req_id":27},"msg_type":"buy","req_id":27}
[2025-07-17 20:58:29] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241790648, BuyPrice=0.35, Payout=0.66, Balance=76291.74
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241790648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241790648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241790648
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241790648, ContractType=Higher
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:58:29] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:29] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":28}
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241790648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":28,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2871.943"},"date_expiry":1752797112,"date_start":1752797108,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2871.943,"spot_time":1752797108,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":28}
[2025-07-17 20:58:29] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:29] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:30] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":29}
[2025-07-17 20:58:30] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":29,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2871.943"},"date_expiry":1752797113,"date_start":1752797109,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2871.943,"spot_time":1752797108,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":29}
[2025-07-17 20:58:30] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:25.873. Criando nova simulação às 20:58:30.878.
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:25.873 (expiração da anterior)
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:25.873, Expiração prevista: 20:58:29.873
[2025-07-17 20:58:30] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205825_4107, Duração=4s
[2025-07-17 20:58:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2871.861,"bid":2871.661,"epoch":1752797110,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.761,"symbol":"R_25"}}
[2025-07-17 20:58:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:31] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:31] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":30}
[2025-07-17 20:58:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":30,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2871.761"},"date_expiry":1752797114,"date_start":1752797110,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2871.761,"spot_time":1752797110,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":30}
[2025-07-17 20:58:31] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:31] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:33] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.073,"bid":2871.873,"epoch":1752797112,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2871.973,"symbol":"R_25"}}
[2025-07-17 20:58:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241790648
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2871.943
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2871.973
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241790648
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241790648
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2871.943
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2871.973
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:58:34] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":31}
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:58:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:58:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":32}
[2025-07-17 20:58:34] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":31,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2871.973"},"date_expiry":1752797117,"date_start":1752797113,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2871.973,"spot_time":1752797112,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":31}
[2025-07-17 20:58:34] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:34] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:34] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":32},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308}]},"req_id":32}
[2025-07-17 20:58:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.147,"bid":2871.947,"epoch":1752797114,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.047,"symbol":"R_25"}}
[2025-07-17 20:58:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:35] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:35] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":33}
[2025-07-17 20:58:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":33,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.047"},"date_expiry":1752797118,"date_start":1752797114,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.047,"spot_time":1752797114,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":33}
[2025-07-17 20:58:35] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:29.873. Criando nova simulação às 20:58:35.871.
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:29.873 (expiração da anterior)
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:29.873, Expiração prevista: 20:58:33.873
[2025-07-17 20:58:35] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205829_7781, Duração=4s
[2025-07-17 20:58:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:36] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":34}
[2025-07-17 20:58:36] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":34,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.047"},"date_expiry":1752797119,"date_start":1752797115,"display_value":"0.35","id":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.047,"spot_time":1752797114,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":34}
[2025-07-17 20:58:36] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:37] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.234,"bid":2872.034,"epoch":**********,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.134,"symbol":"R_25"}}
[2025-07-17 20:58:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=0d71b526-da81-65eb-61e4-a5d2b83dd7c6, Price=0.66
[2025-07-17 20:58:37] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","price":0.66,"req_id":35}
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:58:37] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76292.05,"buy_price":0.35,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"0d71b526-da81-65eb-61e4-a5d2b83dd7c6","price":0.66,"req_id":35},"msg_type":"buy","req_id":35}
[2025-07-17 20:58:37] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.35, Payout=0.66, Balance=76292.05
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:58:37] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:37] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":36}
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:37] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":36,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.134"},"date_expiry":**********,"date_start":**********,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.134,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":36}
[2025-07-17 20:58:37] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:37] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:38] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":37}
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":37,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.134"},"date_expiry":1752797121,"date_start":1752797117,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.134,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":37}
[2025-07-17 20:58:38] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:38] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":38}
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":39}
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":39},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier_count":1,"bid_price":0.64,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2872.134,"current_spot_display_value":"2872.134","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":0.29,"profit_percentage":82.86,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Waiting for entry tick.","validation_error_code":"General"},"req_id":39}
[2025-07-17 20:58:38] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier_count":1,"bid_price":0.64,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2872.134,"current_spot_display_value":"2872.134","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":0.29,"profit_percentage":82.86,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Waiting for entry tick.","validation_error_code":"General"}
[2025-07-17 20:58:38] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":38},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241790648,"payout":0.66,"purchase_time":1752797108,"sell_price":0.66,"sell_time":1752797114,"transaction_id":574411398488},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328}]},"req_id":38}
[2025-07-17 20:58:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:39] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.449,"bid":2872.249,"epoch":1752797118,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.349,"symbol":"R_25"}}
[2025-07-17 20:58:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:40] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:33.873. Criando nova simulação às 20:58:40.879.
[2025-07-17 20:58:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:33.873 (expiração da anterior)
[2025-07-17 20:58:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:33.873, Expiração prevista: 20:58:37.873
[2025-07-17 20:58:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205833_6448, Duração=4s
[2025-07-17 20:58:41] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.857,"bid":2872.657,"epoch":**********,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.757,"symbol":"R_25"}}
[2025-07-17 20:58:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:41] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:41] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":40}
[2025-07-17 20:58:41] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":40,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.757"},"date_expiry":1752797124,"date_start":**********,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.757,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":40}
[2025-07-17 20:58:41] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:41] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2872.134
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2872.757
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2872.134
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2872.757
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:58:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":41}
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:58:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:58:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":42}
[2025-07-17 20:58:42] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":41,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.757"},"date_expiry":1752797125,"date_start":1752797121,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.757,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":41}
[2025-07-17 20:58:42] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:42] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":42},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241790648,"payout":0.66,"purchase_time":1752797108,"sell_price":0.66,"sell_time":1752797114,"transaction_id":574411398488},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328}]},"req_id":42}
[2025-07-17 20:58:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.012,"bid":2872.812,"epoch":1752797122,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.912,"symbol":"R_25"}}
[2025-07-17 20:58:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:45] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.016,"bid":2872.816,"epoch":1752797124,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.916,"symbol":"R_25"}}
[2025-07-17 20:58:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:45] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:45] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":43}
[2025-07-17 20:58:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:58:45] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":43,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.916"},"date_expiry":1752797128,"date_start":1752797124,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.916,"spot_time":1752797124,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":43}
[2025-07-17 20:58:45] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:58:37.873. Criando nova simulação às 20:58:45.866.
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:58:37.873 (expiração da anterior)
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:58:37.873, Expiração prevista: 20:58:41.873
[2025-07-17 20:58:45] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717205837_9008, Duração=4s
[2025-07-17 20:58:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:46] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":44}
[2025-07-17 20:58:46] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":44,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.916"},"date_expiry":1752797129,"date_start":1752797125,"display_value":"0.35","id":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.916,"spot_time":1752797124,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":44}
[2025-07-17 20:58:46] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:47] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.658,"bid":2872.458,"epoch":1752797126,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.558,"symbol":"R_25"}}
[2025-07-17 20:58:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=567b2ba9-eeb9-d89c-ed33-8c406d7eb652, Price=0.66
[2025-07-17 20:58:47] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","price":0.66,"req_id":45}
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:58:47] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76292.36,"buy_price":0.35,"contract_id":288241806648,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752797126,"shortcode":"CALL_R_25_0.66_1752797126_2T_S0P_0","start_time":1752797126,"transaction_id":574411430048},"echo_req":{"buy":"567b2ba9-eeb9-d89c-ed33-8c406d7eb652","price":0.66,"req_id":45},"msg_type":"buy","req_id":45}
[2025-07-17 20:58:47] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241806648, BuyPrice=0.35, Payout=0.66, Balance=76292.36
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241806648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241806648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241806648
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241806648, ContractType=Higher
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:58:47] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:47] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":46}
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241806648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:58:47] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":46,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.558"},"date_expiry":1752797130,"date_start":1752797126,"display_value":"0.35","id":"d3a0d2cf-bbde-bc24-2cd3-de05ff81acc9","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.558,"spot_time":1752797126,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":46}
[2025-07-17 20:58:47] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:47] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:48] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:58:48] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":47}
[2025-07-17 20:58:48] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":47,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2872.558"},"date_expiry":1752797131,"date_start":1752797127,"display_value":"0.35","id":"d3a0d2cf-bbde-bc24-2cd3-de05ff81acc9","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2872.558,"spot_time":1752797126,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":47}
[2025-07-17 20:58:48] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:48] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:58:48] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.617,"bid":2872.417,"epoch":1752797128,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.517,"symbol":"R_25"}}
[2025-07-17 20:58:49] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:58:49] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Parando simulação automática
[2025-07-17 20:58:51] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2872.683,"bid":2872.483,"epoch":1752797130,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.583,"symbol":"R_25"}}
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241806648
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2872.558
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2872.583
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241806648
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241806648
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2872.558
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2872.583
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:58:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:58:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":48}
[2025-07-17 20:58:52] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":48},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0.66,"sell_time":1752797122,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241790648,"payout":0.66,"purchase_time":1752797108,"sell_price":0.66,"sell_time":1752797114,"transaction_id":574411398488},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608}]},"req_id":48}
[2025-07-17 20:58:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.023,"bid":2872.823,"epoch":1752797132,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2872.923,"symbol":"R_25"}}
[2025-07-17 20:58:53] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":49}
[2025-07-17 20:58:54] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":49},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241806648,"payout":0.66,"purchase_time":1752797126,"sell_price":0.66,"sell_time":1752797132,"transaction_id":574411430048},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0.66,"sell_time":1752797122,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241790648,"payout":0.66,"purchase_time":1752797108,"sell_price":0.66,"sell_time":1752797114,"transaction_id":574411398488},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":1.44,"sell_time":1752797106,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241773868,"payout":0.66,"purchase_time":1752797091,"sell_price":0,"sell_time":1752797096,"transaction_id":574411365248},{"app_id":82663,"buy_price":1.54,"contract_id":288241321168,"payout":3.01,"purchase_time":1752796620,"sell_price":3.01,"sell_time":1752796627,"transaction_id":574410459068},{"app_id":82663,"buy_price":0.74,"contract_id":288241313408,"payout":1.44,"purchase_time":1752796612,"sell_price":1.44,"sell_time":1752796618,"transaction_id":574410443468},{"app_id":82663,"buy_price":0.35,"contract_id":288241303528,"payout":0.66,"purchase_time":1752796600,"sell_price":0,"sell_time":1752796606,"transaction_id":574410423888},{"app_id":82663,"buy_price":0.35,"contract_id":288241293948,"payout":0.66,"purchase_time":1752796590,"sell_price":0.66,"sell_time":1752796596,"transaction_id":574410405268},{"app_id":82663,"buy_price":0.35,"contract_id":288241284488,"payout":0.66,"purchase_time":1752796580,"sell_price":0.66,"sell_time":1752796586,"transaction_id":574410386648},{"app_id":82663,"buy_price":0.35,"contract_id":288241274648,"payout":0.66,"purchase_time":1752796571,"sell_price":0,"sell_time":1752796576,"transaction_id":574410367188},{"app_id":82663,"buy_price":0.35,"contract_id":288241264188,"payout":0.66,"purchase_time":1752796560,"sell_price":0.66,"sell_time":1752796566,"transaction_id":574410346388},{"app_id":82663,"buy_price":0.35,"contract_id":288241253188,"payout":0.66,"purchase_time":1752796550,"sell_price":0,"sell_time":1752796556,"transaction_id":574410325368},{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":288241029248,"payout":0.66,"purchase_time":1752796337,"sell_price":0.66,"sell_time":1752796342,"transaction_id":574409884408},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":288241012988,"payout":1.44,"purchase_time":1752796322,"sell_price":0,"sell_time":1752796329,"transaction_id":574409852008},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308}]},"req_id":49}
[2025-07-17 20:58:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.31,"bid":2873.11,"epoch":1752797134,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.21,"symbol":"R_25"}}
[2025-07-17 20:58:57] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.368,"bid":2873.168,"epoch":1752797136,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.268,"symbol":"R_25"}}
[2025-07-17 20:58:59] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.405,"bid":2873.205,"epoch":1752797138,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.305,"symbol":"R_25"}}
[2025-07-17 20:59:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.446,"bid":2873.246,"epoch":1752797140,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.346,"symbol":"R_25"}}
[2025-07-17 20:59:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.418,"bid":2873.218,"epoch":1752797142,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.318,"symbol":"R_25"}}
[2025-07-17 20:59:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885"},"tick":{"ask":2873.347,"bid":2873.147,"epoch":1752797144,"id":"dcf1e4de-9ee2-73ad-0a8b-f0b97ed52885","pip_size":3,"quote":2873.247,"symbol":"R_25"}}
[2025-07-17 20:59:05] [Information] Excalibur.Services.AppLoggerService: Conectando evento ContractSimulated...
[2025-07-17 20:59:05] [Information] Excalibur.Services.AppLoggerService: Evento ContractSimulated conectado com sucesso!
[2025-07-17 20:59:05] [Information] Excalibur.Services.AppLoggerService: Configurando simulação inicial: Amostra=1, Win=1, Loss=0
[2025-07-17 20:59:05] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":50}
[2025-07-17 20:59:05] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:59:05] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:59:05] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:59:05] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:59:05] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:59:05] [Error] Excalibur.Core.Services.DerivApiService: Erro ao receber mensagem
