[2025-07-17 20:44:40] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:44:40] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:44:40] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Carregando configurações salvas
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' alterado para: ATIVADO
[2025-07-17 20:44:40] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-SETTINGS] Configurações carregadas: Stake=0.35, Duration=2, DurationType=t
[2025-07-17 20:44:40] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Carregando configurações salvas
[2025-07-17 20:44:40] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:44:40] [Information] Excalibur.ViewModels.MoneyManagementViewModel: [MM-SETTINGS] Configurações carregadas: Factor=2.1, Level=8, Enabled=True
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:40] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:44:40] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:44:40] [Error] Excalibur.Services.SettingsService: [SETTINGS] Erro ao salvar configurações
[2025-07-17 20:44:43] [Information] Excalibur.Core.Services.DerivApiService: Conectado ao WebSocket da Deriv
[2025-07-17 20:44:43] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"authorize":"00I1wVoORm3kOYZ","req_id":2}
[2025-07-17 20:44:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"authorize":{"account_list":[{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR799153"},{"account_category":"trading","account_type":"binary","broker":"CR","created_at":**********,"currency":"BTC","currency_type":"crypto","is_disabled":0,"is_virtual":0,"landing_company_name":"svg","linked_to":[],"loginid":"CR917656"},{"account_category":"trading","account_type":"binary","broker":"VRTC","created_at":**********,"currency":"USD","currency_type":"fiat","is_disabled":0,"is_virtual":1,"landing_company_name":"virtual","linked_to":[],"loginid":"VRTC2033220"}],"balance":76289.54,"country":"br","currency":"USD","email":"<EMAIL>","fullname":"Mr Julio Cesar Ferreira Da Silva","is_virtual":1,"landing_company_fullname":"Deriv Limited","landing_company_name":"virtual","linked_to":[],"local_currencies":{"BRL":{"fractional_digits":2}},"loginid":"VRTC2033220","preferred_language":"EN","scopes":["read","trade","trading_information"],"upgradeable_landing_companies":["maltainvest","svg"],"user_id":6145887},"echo_req":{"authorize":"<not shown>","req_id":2},"msg_type":"authorize","req_id":2}
[2025-07-17 20:44:43] [Information] Excalibur.Core.Services.DerivApiService: Account info received: VRTC2033220, Balance: 76289.54 USD
[2025-07-17 20:44:43] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"active_symbols":"brief","req_id":3}
[2025-07-17 20:44:44] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"active_symbols":[{"allow_forward_starting":0,"close_only":0,"display_name":"AUD Basket","display_order":30,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDAUD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CAD","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/CHF","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/JPY","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/NZD","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxAUDNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"AUD\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxAUDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Australia 200","display_order":4,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_AS51","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"BTC\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryBTCUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":1,"close_only":0,"display_name":"Bear Market Index","display_order":10,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBEAR","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 300 Index","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 500 Index","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 600 Index","display_order":31,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 900 Index","display_order":36,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Boom 1000 Index","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"BOOM1000","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Bull Market Index","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_daily","submarket_display_name":"Daily Reset Indices","symbol":"RDBULL","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 300 Index","display_order":28,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH300N","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 500 Index","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH500","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 600 Index","display_order":32,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH600","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 900 Index","display_order":34,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH900","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"Crash 1000 Index","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"crash_index","submarket_display_name":"Crash\/Boom Indices","symbol":"CRASH1000","symbol_type":""},{"allow_forward_starting":0,"close_only":0,"display_name":"ETH\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"cryptocurrency","market_display_name":"Cryptocurrencies","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"non_stable_coin","submarket_display_name":"Cryptocurrencies","symbol":"cryETHUSD","symbol_type":"cryptocurrency"},{"allow_forward_starting":0,"close_only":0,"display_name":"EUR Basket","display_order":37,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDEUR","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/AUD","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CAD","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/CHF","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/GBP","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURGBP","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/JPY","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/NZD","display_order":19,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxEURNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"EUR\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxEURUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Euro 50","display_order":8,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SX5E","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"France 40","display_order":9,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FCHI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"GBP Basket","display_order":38,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDGBP","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/AUD","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPAUD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CAD","display_order":18,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/CHF","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/JPY","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NOK","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/NZD","display_order":21,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxGBPNZD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"GBP\/USD","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxGBPUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Germany 40","display_order":7,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_GDAXI","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Gold Basket","display_order":33,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"commodity_basket","submarket_display_name":"Commodities Basket","symbol":"WLDXAU","symbol_type":"commodity_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"Gold\/USD","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAUUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Hong Kong 50","display_order":10,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_HSI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Japan 225","display_order":5,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"asia_oceania_OTC","submarket_display_name":"Asian indices","symbol":"OTC_N225","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 10 Index","display_order":11,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 25 Index","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 50 Index","display_order":17,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 75 Index","display_order":14,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Jump 100 Index","display_order":13,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"jump_index","submarket_display_name":"Jump Indices","symbol":"JD100","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/JPY","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"NZD\/USD","display_order":16,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxNZDUSD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Netherlands 25","display_order":11,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_AEX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Palladium\/USD","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPDUSD","symbol_type":"commodities"},{"allow_forward_starting":0,"close_only":0,"display_name":"Platinum\/USD","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXPTUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Silver\/USD","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"commodities","market_display_name":"Commodities","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"metals","submarket_display_name":"Metals","symbol":"frxXAGUSD","symbol_type":"commodities"},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 100","display_order":15,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 200","display_order":23,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG2","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 300","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG3","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 400","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG4","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Step Index 500","display_order":20,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.1,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"step_index","submarket_display_name":"Step Indices","symbol":"stpRNG5","symbol_type":""},{"allow_forward_starting":1,"close_only":0,"display_name":"Swiss 20","display_order":6,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_SSMI","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"UK 100","display_order":3,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"europe_OTC","submarket_display_name":"European indices","symbol":"OTC_FTSE","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US 500","display_order":0,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_SPC","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"US Tech 100","display_order":1,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_NDX","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"USD Basket","display_order":35,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"baskets","subgroup_display_name":"Baskets","submarket":"forex_basket","submarket_display_name":"Forex Basket","symbol":"WLDUSD","symbol_type":"forex_basket"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CAD","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCAD","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/CHF","display_order":12,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDCHF","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/JPY","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.001,"subgroup":"none","subgroup_display_name":"None","submarket":"major_pairs","submarket_display_name":"Major Pairs","symbol":"frxUSDJPY","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/MXN","display_order":22,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDMXN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/NOK","display_order":26,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDNOK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/PLN","display_order":24,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":0.0001,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDPLN","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"USD\/SEK","display_order":25,"exchange_is_open":1,"is_trading_suspended":0,"market":"forex","market_display_name":"Forex","pip":1e-05,"subgroup":"none","subgroup_display_name":"None","submarket":"minor_pairs","submarket_display_name":"Minor Pairs","symbol":"frxUSDSEK","symbol_type":"forex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 (1s) Index","display_order":2,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ10V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 10 Index","display_order":3,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_10","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 (1s) Index","display_order":5,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ25V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 25 Index","display_order":8,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_25","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 (1s) Index","display_order":9,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ50V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 50 Index","display_order":4,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_50","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 (1s) Index","display_order":6,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ75V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 75 Index","display_order":7,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.0001,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_75","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 (1s) Index","display_order":1,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ100V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Volatility 100 Index","display_order":0,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"R_100","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 150 (1s) Index","display_order":27,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ150V","symbol_type":"stockindex"},{"allow_forward_starting":0,"close_only":0,"display_name":"Volatility 250 (1s) Index","display_order":29,"exchange_is_open":1,"is_trading_suspended":0,"market":"synthetic_index","market_display_name":"Derived","pip":0.01,"subgroup":"synthetics","subgroup_display_name":"Synthetics","submarket":"random_index","submarket_display_name":"Continuous Indices","symbol":"1HZ250V","symbol_type":"stockindex"},{"allow_forward_starting":1,"close_only":0,"display_name":"Wall Street 30","display_order":2,"exchange_is_open":0,"is_trading_suspended":0,"market":"indices","market_display_name":"Stock Indices","pip":0.01,"subgroup":"none","subgroup_display_name":"None","submarket":"americas_OTC","submarket_display_name":"American indices","symbol":"OTC_DJI","symbol_type":"stockindex"}],"echo_req":{"active_symbols":"brief","req_id":3},"msg_type":"active_symbols","req_id":3}
[2025-07-17 20:44:44] [Information] Excalibur.Core.Services.DerivApiService: Active symbols received: 85 symbols
[2025-07-17 20:44:44] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Clearing existing symbols and adding 85 new symbols
[2025-07-17 20:44:44] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Symbols collection populated with 85 items
[2025-07-17 20:44:49] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Carregando contratos para R_25
[2025-07-17 20:44:49] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"contracts_for":"R_25","req_id":4}
[2025-07-17 20:44:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"contracts_for":{"available":[{"barrier_category":"american","barriers":2,"contract_category":"accumulator","contract_category_display":"Accumulator","contract_display":"Accumulator Up","contract_type":"ACCU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","growth_rate_range":[0.01,0.02,0.03,0.04,0.05],"high_barrier":"+0.000","low_barrier":"+0.000","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Up","contract_type":"ASIANU","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"asian","barriers":0,"contract_category":"asian","contract_category_display":"Asians","contract_display":"Asian Down","contract_type":"ASIAND","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2908.687","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2908.687","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.417","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Higher","contract_type":"CALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"euro_non_atm","barriers":1,"contract_category":"callput","contract_category_display":"Up\/Down","contract_display":"Lower","contract_type":"PUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"up","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","forward_starting_options":[{"close":"**********","date":"**********","open":"**********"},{"close":"1752883199","date":"1752796800","open":"1752796800"},{"close":"1752969599","date":"1752883200","open":"1752883200"}],"market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"down","start_type":"forward","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"15s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Higher","contract_type":"CALLE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"callputequal","contract_category_display":"Rise\/Fall Equal","contract_display":"Lower","contract_type":"PUTE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Matches","contract_type":"DIGITMATCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"match","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Differs","contract_type":"DIGITDIFF","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"differ","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Odd","contract_type":"DIGITODD","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"odd","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":0,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Even","contract_type":"DIGITEVEN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"even","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Over","contract_type":"DIGITOVER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[0,1,2,3,4,5,6,7,8],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"over","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"non_financial","barriers":1,"contract_category":"digits","contract_category_display":"Digits","contract_display":"Digit Under","contract_type":"DIGITUNDER","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","last_digit_range":[1,2,3,4,5,6,7,8,9],"market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"1t","sentiment":"under","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2908.687","low_barrier":"2845.320","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2908.687","low_barrier":"2845.320","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Outside","contract_type":"EXPIRYMISS","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_non_atm","barriers":2,"contract_category":"endsinout","contract_category_display":"Ends Between\/Ends Outside","contract_display":"Ends Between","contract_type":"EXPIRYRANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"High Tick","contract_type":"TICKHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"high","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"contract_category":"highlowticks","contract_category_display":"High\/Low Ticks","contract_display":"Low Tick","contract_type":"TICKLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"5t","sentiment":"low","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"Close-Low","contract_type":"LBFLOATCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Close","contract_type":"LBFLOATPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"lookback","barriers":0,"contract_category":"lookback","contract_category_display":"Lookbacks","contract_display":"High-Low","contract_type":"LBHIGHLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"30m","min_contract_duration":"1m","sentiment":"updown","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Up","contract_type":"MULTUP","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":0,"cancellation_range":["5m","10m","15m","30m","60m"],"contract_category":"multiplier","contract_category_display":"Multiply Up\/Multiply Down","contract_display":"Multiply Down","contract_type":"MULTDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"no_expiry","market":"synthetic_index","max_contract_duration":"0","min_contract_duration":"0","multiplier_range":[50,400,800,1200,1600],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.482","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"2h","min_contract_duration":"20s","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Call","contract_type":"RESETCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"reset","barriers":1,"contract_category":"reset","contract_category_display":"Reset Call\/Reset Put","contract_display":"Reset Put","contract_type":"RESETPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Ups","contract_type":"RUNHIGH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.152","barrier_category":"american","barriers":1,"contract_category":"runs","contract_category_display":"Only Ups\/Only Downs","contract_display":"Only Downs","contract_type":"RUNLOW","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"5t","min_contract_duration":"2t","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2908.687","low_barrier":"2845.320","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","high_barrier":"2908.687","low_barrier":"2845.320","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Stays Between","contract_type":"RANGE","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"american","barriers":2,"contract_category":"staysinout","contract_category_display":"Stays Between\/Goes Outside","contract_display":"Goes Outside","contract_type":"UPORDOWN","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","high_barrier":"+1.181","low_barrier":"-1.180","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2908.687","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2908.687","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.181","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+1.181","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"2m","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Touches","contract_type":"ONETOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"high_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.241","barrier_category":"american","barriers":1,"contract_category":"touchnotouch","contract_category_display":"Touch\/No Touch","contract_display":"Does Not Touch","contract_type":"NOTOUCH","default_stake":10,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","min_contract_duration":"5t","sentiment":"low_vol","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.46,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","max_stake":28135.46,"min_contract_duration":"1d","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.46,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","max_stake":28135.46,"min_contract_duration":"15s","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Long","contract_type":"TURBOSLONG","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.46,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"80.934","barrier_category":"american","barrier_choices":["2.219","4.555","9.351","19.199","39.419","80.934","166.172","341.183","700.514","1438.292"],"barriers":1,"contract_category":"turbos","contract_category_display":"Turbos Options","contract_display":"Turbos Short","contract_type":"TURBOSSHORT","default_stake":10,"display_number_of_contracts":2.4,"exchange_name":"RANDOM","expiry_type":"tick","market":"synthetic_index","max_contract_duration":"10t","max_stake":28135.46,"min_contract_duration":"5t","min_stake":1.41,"payout_choices":[4,3.2,2.4,1.6,0.8],"sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2880.000","barrier_category":"euro_non_atm","barrier_choices":["2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000","2920.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"2880.000","barrier_category":"euro_non_atm","barrier_choices":["2840.000","2850.000","2860.000","2870.000","2880.000","2890.000","2900.000","2910.000","2920.000"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"daily","market":"synthetic_index","max_contract_duration":"365d","min_contract_duration":"1d","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier_category":"euro_atm","barriers":0,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Call","contract_type":"VANILLALONGCALL","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"up","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"},{"barrier":"+0.000","barrier_category":"euro_non_atm","barrier_choices":["+1.270","+0.670","+0.000","-0.670","-1.270"],"barriers":1,"contract_category":"vanilla","contract_category_display":"Vanilla Options","contract_display":"Vanilla Long Put","contract_type":"VANILLALONGPUT","default_stake":10,"exchange_name":"RANDOM","expiry_type":"intraday","market":"synthetic_index","max_contract_duration":"1d","min_contract_duration":"1m","sentiment":"down","start_type":"spot","submarket":"random_index","underlying_symbol":"R_25"}],"close":**********,"feed_license":"realtime","hit_count":72,"non_available":[],"open":**********,"spot":2876.583},"echo_req":{"contracts_for":"R_25","req_id":4},"msg_type":"contracts_for","req_id":4}
[2025-07-17 20:44:49] [Information] Excalibur.Core.Services.DerivApiService: Contracts received for R_25: 72 contracts
[2025-07-17 20:44:49] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contratos carregados para R_25: 72 contratos
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ActiveSymbolsViewModel: Contrato selecionado: Higher para R_25
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Símbolo selecionado: R_25
[2025-07-17 20:44:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"ticks":"R_25","subscribe":1,"req_id":5}
[2025-07-17 20:44:52] [Warning] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato ou símbolo não selecionado
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Contrato selecionado: Higher (CALL)
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:44:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":6}
[2025-07-17 20:44:52] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Detalhes do contrato:
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractDisplay: Higher
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel:   ContractTypeValue: CALL
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowBarrierField: False
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel:   ShowDigitField: False
[2025-07-17 20:44:52] [Information] Excalibur.ViewModels.ProposalViewModel:   BarrierChoices: null
[2025-07-17 20:44:52] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:44:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":6,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.808"},"date_expiry":1752796296,"date_start":1752796292,"display_value":"0.35","id":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.808,"spot_time":1752796290,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":6}
[2025-07-17 20:44:53] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:44:53] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:44:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.887,"bid":2876.687,"epoch":1752796292,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.787,"symbol":"R_25"}}
[2025-07-17 20:44:54] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":7}
[2025-07-17 20:44:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":7},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568},{"app_id":82663,"buy_price":0.74,"contract_id":288132692768,"payout":1.44,"purchase_time":1752709326,"sell_price":0,"sell_time":1752709332,"transaction_id":574199583368},{"app_id":82663,"buy_price":0.35,"contract_id":288132682708,"payout":0.66,"purchase_time":1752709316,"sell_price":0.66,"sell_time":1752709322,"transaction_id":574199563908}]},"req_id":7}
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Iniciando simulação automática contínua
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Solicitando limpeza das tabelas Simulação e Compras
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Toggle 'Manter' está: ATIVADO
[2025-07-17 20:44:55] [Information] Excalibur.Services.AppLoggerService: Fila de compras pendentes limpa
[2025-07-17 20:44:55] [Information] Excalibur.Services.AppLoggerService: Martingale resetado para valor inicial
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Timer de simulação automática iniciado. Primeira simulação será criada em breve.
[2025-07-17 20:44:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.892,"bid":2876.692,"epoch":1752796294,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.792,"symbol":"R_25"}}
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação automática será criada.
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Primeira simulação iniciará às 20:44:55.747
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:44:55.747, Expiração prevista: 20:44:59.747
[2025-07-17 20:44:55] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204455_7313, Duração=4s
[2025-07-17 20:44:56] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:56] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:44:56] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":8}
[2025-07-17 20:44:56] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":8,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.792"},"date_expiry":1752796299,"date_start":1752796295,"display_value":"0.35","id":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.792,"spot_time":1752796294,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":8}
[2025-07-17 20:44:56] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:44:56] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:44:56] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:57] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:57] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.953,"bid":2876.753,"epoch":1752796296,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.853,"symbol":"R_25"}}
[2025-07-17 20:44:57] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:58] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:58] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:59] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.692,"bid":2876.492,"epoch":1752796298,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.592,"symbol":"R_25"}}
[2025-07-17 20:44:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:44:59] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:44:59] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:44:59] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":9}
[2025-07-17 20:44:59] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:44:59] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:00] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":9,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.592"},"date_expiry":1752796303,"date_start":1752796299,"display_value":"0.35","id":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.592,"spot_time":1752796298,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":9}
[2025-07-17 20:45:00] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:00] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:00] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Aguardando expiração da simulação anterior. Tempo restante: -0.5s
[2025-07-17 20:45:00] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Aguardando período de segurança após expiração
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:44:59.747. Criando nova simulação às 20:45:01.232.
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:44:59.747 (expiração da anterior)
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:44:59.747, Expiração prevista: 20:45:03.747
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204459_2882, Duração=4s
[2025-07-17 20:45:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.82,"bid":2876.62,"epoch":1752796300,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.72,"symbol":"R_25"}}
[2025-07-17 20:45:01] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:01] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":10}
[2025-07-17 20:45:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":10,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.720"},"date_expiry":1752796304,"date_start":1752796300,"display_value":"0.35","id":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.72,"spot_time":1752796300,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":10}
[2025-07-17 20:45:01] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:01] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:02] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4, Price=0.66
[2025-07-17 20:45:02] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","price":0.66,"req_id":11}
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:02] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.19,"buy_price":0.35,"contract_id":288240990648,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796301,"shortcode":"CALL_R_25_0.66_1752796301_2T_S0P_0","start_time":1752796301,"transaction_id":574409806148},"echo_req":{"buy":"0ca32b0e-f5ae-73ec-d4ee-3821c5b43aa4","price":0.66,"req_id":11},"msg_type":"buy","req_id":11}
[2025-07-17 20:45:02] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288240990648, BuyPrice=0.35, Payout=0.66, Balance=76289.19
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288240990648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288240990648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288240990648
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288240990648, ContractType=Higher
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:02] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:02] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":12}
[2025-07-17 20:45:02] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] Stake resetada para valor base: 0.35
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288240990648, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:02] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:02] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":12,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.720"},"date_expiry":1752796305,"date_start":1752796301,"display_value":"0.35","id":"3b0d3c76-66c6-c133-7a32-954797eebc32","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.72,"spot_time":1752796300,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":12}
[2025-07-17 20:45:02] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:02] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.72,"bid":2876.52,"epoch":1752796302,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.62,"symbol":"R_25"}}
[2025-07-17 20:45:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:03] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":13}
[2025-07-17 20:45:03] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":13,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.620"},"date_expiry":1752796306,"date_start":1752796302,"display_value":"0.35","id":"3b0d3c76-66c6-c133-7a32-954797eebc32","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.62,"spot_time":1752796302,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":13}
[2025-07-17 20:45:03] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:03] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:03] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:04] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:04] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:05] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:05] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.565,"bid":2876.365,"epoch":1752796304,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.465,"symbol":"R_25"}}
[2025-07-17 20:45:05] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:03.747. Criando nova simulação às 20:45:06.238.
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:03.747 (expiração da anterior)
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:03.747, Expiração prevista: 20:45:07.747
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204503_4091, Duração=4s
[2025-07-17 20:45:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":14}
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288240990648
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2876.720
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2876.465
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: -0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: PERDA
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288240990648
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288240990648
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2876.720
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2876.465
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: -0.35
[2025-07-17 20:45:06] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":14,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2876.465"},"date_expiry":1752796309,"date_start":1752796305,"display_value":"0.35","id":"3b0d3c76-66c6-c133-7a32-954797eebc32","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2876.465,"spot_time":1752796304,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":14}
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: PERDA
[2025-07-17 20:45:06] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: True
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.74
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: True
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:45:06] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:06] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:06] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":15}
[2025-07-17 20:45:07] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":15},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568},{"app_id":82663,"buy_price":0.74,"contract_id":288132692768,"payout":1.44,"purchase_time":1752709326,"sell_price":0,"sell_time":1752709332,"transaction_id":574199583368},{"app_id":82663,"buy_price":0.35,"contract_id":288132682708,"payout":0.66,"purchase_time":1752709316,"sell_price":0.66,"sell_time":1752709322,"transaction_id":574199563908}]},"req_id":15}
[2025-07-17 20:45:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:07] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2876.964,"bid":2876.764,"epoch":1752796306,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2876.864,"symbol":"R_25"}}
[2025-07-17 20:45:07] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:08] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:09] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.343,"bid":2877.143,"epoch":1752796308,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.243,"symbol":"R_25"}}
[2025-07-17 20:45:09] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:09] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":16}
[2025-07-17 20:45:09] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:09] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":17}
[2025-07-17 20:45:10] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":17,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.243"},"date_expiry":1752796313,"date_start":1752796309,"display_value":"0.74","id":"35d8fd73-0430-4589-ae0a-0dd43691eac7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.243,"spot_time":1752796308,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":17}
[2025-07-17 20:45:10] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:10] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":16},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568},{"app_id":82663,"buy_price":0.74,"contract_id":288132692768,"payout":1.44,"purchase_time":1752709326,"sell_price":0,"sell_time":1752709332,"transaction_id":574199583368}]},"req_id":16}
[2025-07-17 20:45:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:10] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:10] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:10] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":18}
[2025-07-17 20:45:10] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":18,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.243"},"date_expiry":1752796314,"date_start":1752796310,"display_value":"0.35","id":"3b0d3c76-66c6-c133-7a32-954797eebc32","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.243,"spot_time":1752796308,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":18}
[2025-07-17 20:45:11] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:07.747. Criando nova simulação às 20:45:11.243.
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:07.747 (expiração da anterior)
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:07.747, Expiração prevista: 20:45:11.747
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204507_6634, Duração=4s
[2025-07-17 20:45:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.636,"bid":2877.436,"epoch":1752796310,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.536,"symbol":"R_25"}}
[2025-07-17 20:45:11] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:11] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":19}
[2025-07-17 20:45:11] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":19,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.536"},"date_expiry":1752796314,"date_start":1752796310,"display_value":"0.35","id":"3b0d3c76-66c6-c133-7a32-954797eebc32","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.536,"spot_time":1752796310,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":19}
[2025-07-17 20:45:11] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:11] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:12] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=3b0d3c76-66c6-c133-7a32-954797eebc32, Price=0.66
[2025-07-17 20:45:12] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"3b0d3c76-66c6-c133-7a32-954797eebc32","price":0.66,"req_id":20}
[2025-07-17 20:45:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:12] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.5,"buy_price":0.35,"contract_id":288241001968,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796312,"shortcode":"CALL_R_25_0.66_1752796312_2T_S0P_0","start_time":1752796312,"transaction_id":574409829708},"echo_req":{"buy":"3b0d3c76-66c6-c133-7a32-954797eebc32","price":0.66,"req_id":20},"msg_type":"buy","req_id":20}
[2025-07-17 20:45:13] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241001968, BuyPrice=0.35, Payout=0.66, Balance=76289.5
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241001968, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241001968, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241001968
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241001968, ContractType=Higher
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:13] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:13] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":21}
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241001968, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.576,"bid":2877.376,"epoch":1752796312,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.476,"symbol":"R_25"}}
[2025-07-17 20:45:13] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":21,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.476"},"date_expiry":1752796316,"date_start":1752796312,"display_value":"0.35","id":"21bae406-5a3e-48dd-fd79-d799caaa6804","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.476,"spot_time":1752796312,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":21}
[2025-07-17 20:45:13] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:13] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:13] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:13] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":22}
[2025-07-17 20:45:14] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":22,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.476"},"date_expiry":1752796317,"date_start":1752796313,"display_value":"0.35","id":"21bae406-5a3e-48dd-fd79-d799caaa6804","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.476,"spot_time":1752796312,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":22}
[2025-07-17 20:45:14] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:14] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:14] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:15] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:15] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.634,"bid":2877.434,"epoch":1752796314,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.534,"symbol":"R_25"}}
[2025-07-17 20:45:15] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:11.747. Criando nova simulação às 20:45:16.233.
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:11.747 (expiração da anterior)
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:11.747, Expiração prevista: 20:45:15.747
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204511_1543, Duração=4s
[2025-07-17 20:45:16] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:16] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":23}
[2025-07-17 20:45:16] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":23,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.534"},"date_expiry":1752796319,"date_start":1752796315,"display_value":"0.35","id":"21bae406-5a3e-48dd-fd79-d799caaa6804","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.534,"spot_time":1752796314,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":23}
[2025-07-17 20:45:16] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:16] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:17] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.556,"bid":2877.356,"epoch":1752796316,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.456,"symbol":"R_25"}}
[2025-07-17 20:45:17] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241001968
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2877.536
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2877.456
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: -0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: PERDA
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241001968
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241001968
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2877.536
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2877.456
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: -0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: PERDA
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: True
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.74
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: True
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:45:17] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:17] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":24}
[2025-07-17 20:45:17] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:17] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":25}
[2025-07-17 20:45:18] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":24,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.456"},"date_expiry":1752796321,"date_start":1752796317,"display_value":"0.74","id":"35d8fd73-0430-4589-ae0a-0dd43691eac7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.456,"spot_time":1752796316,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":24}
[2025-07-17 20:45:18] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:18] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:18] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":25},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568},{"app_id":82663,"buy_price":0.74,"contract_id":288132692768,"payout":1.44,"purchase_time":1752709326,"sell_price":0,"sell_time":1752709332,"transaction_id":574199583368}]},"req_id":25}
[2025-07-17 20:45:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:18] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:19] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.666,"bid":2877.466,"epoch":1752796318,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.566,"symbol":"R_25"}}
[2025-07-17 20:45:19] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:20] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:20] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:20] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":26}
[2025-07-17 20:45:20] [Information] Excalibur.Services.AppLoggerService: Martingale está ativo na tabela Compras (nível 1), adicionando à fila
[2025-07-17 20:45:20] [Information] Excalibur.Services.AppLoggerService: Compra adicionada à fila. Total na fila: sim
[2025-07-17 20:45:20] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":26,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.566"},"date_expiry":1752796324,"date_start":1752796320,"display_value":"0.74","id":"35d8fd73-0430-4589-ae0a-0dd43691eac7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.566,"spot_time":1752796318,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":26}
[2025-07-17 20:45:21] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:15.747. Criando nova simulação às 20:45:21.244.
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:15.747 (expiração da anterior)
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:15.747, Expiração prevista: 20:45:19.747
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204515_2416, Duração=4s
[2025-07-17 20:45:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.704,"bid":2877.504,"epoch":1752796320,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.604,"symbol":"R_25"}}
[2025-07-17 20:45:21] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:21] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":27}
[2025-07-17 20:45:21] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":27,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.604"},"date_expiry":1752796324,"date_start":1752796320,"display_value":"0.74","id":"35d8fd73-0430-4589-ae0a-0dd43691eac7","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.604,"spot_time":1752796320,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":27}
[2025-07-17 20:45:21] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:21] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:22] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=35d8fd73-0430-4589-ae0a-0dd43691eac7, Price=1.44
[2025-07-17 20:45:22] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"35d8fd73-0430-4589-ae0a-0dd43691eac7","price":1.44,"req_id":28}
[2025-07-17 20:45:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:22] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2877.791,"bid":2877.591,"epoch":**********,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.691,"symbol":"R_25"}}
[2025-07-17 20:45:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.42,"buy_price":0.74,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"35d8fd73-0430-4589-ae0a-0dd43691eac7","price":1.44,"req_id":28},"msg_type":"buy","req_id":28}
[2025-07-17 20:45:23] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.74, Payout=1.44, Balance=76289.42
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:23] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:23] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":29}
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,74, Payout: R$ 1,44
[2025-07-17 20:45:23] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":29,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.691"},"date_expiry":**********,"date_start":**********,"display_value":"0.74","id":"8b071445-439d-4ee6-49a3-6fa6d98c56d2","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.691,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":29}
[2025-07-17 20:45:23] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:23] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:23] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:23] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":30}
[2025-07-17 20:45:24] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":30,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2877.691"},"date_expiry":1752796327,"date_start":1752796323,"display_value":"0.74","id":"8b071445-439d-4ee6-49a3-6fa6d98c56d2","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2877.691,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":30}
[2025-07-17 20:45:24] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:24] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:24] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:24] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":31}
[2025-07-17 20:45:24] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:45:24] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":32}
[2025-07-17 20:45:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":32},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier_count":1,"bid_price":1.42,"buy_price":0.74,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2877.691,"current_spot_display_value":"2877.691","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"profit":0.68,"profit_percentage":91.89,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Waiting for entry tick.","validation_error_code":"General"},"req_id":32}
[2025-07-17 20:45:25] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier_count":1,"bid_price":1.42,"buy_price":0.74,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2877.691,"current_spot_display_value":"2877.691","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"profit":0.68,"profit_percentage":91.89,"purchase_time":**********,"shortcode":"CALL_R_25_1.44_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Waiting for entry tick.","validation_error_code":"General"}
[2025-07-17 20:45:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":31},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568}]},"req_id":31}
[2025-07-17 20:45:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:25] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.147,"bid":2877.947,"epoch":1752796324,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.047,"symbol":"R_25"}}
[2025-07-17 20:45:25] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:19.747. Criando nova simulação às 20:45:26.245.
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:19.747 (expiração da anterior)
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.74, Duration=2t (4s)
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:19.747, Expiração prevista: 20:45:23.747
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204519_5349, Duração=4s
[2025-07-17 20:45:26] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.74, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:26] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":33}
[2025-07-17 20:45:26] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.74,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":33,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.74,"contract_details":{"barrier":"2878.047"},"date_expiry":1752796329,"date_start":1752796325,"display_value":"0.74","id":"8b071445-439d-4ee6-49a3-6fa6d98c56d2","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":1.44,"spot":2878.047,"spot_time":1752796324,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":33}
[2025-07-17 20:45:26] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:26] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=1.44, AskPrice=0.74
[2025-07-17 20:45:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:27] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.041,"bid":2877.841,"epoch":**********,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.941,"symbol":"R_25"}}
[2025-07-17 20:45:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2877.604
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2877.941
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.74
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.70
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2877.604
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2877.941
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.74
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.70
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.74
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: True
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 1
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.74
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:45:27] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:27] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":34}
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.74 → 0.35
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: Processando fila de compras pendentes
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: Executando compra pendente da fila (criada em: 20:45:20)
[2025-07-17 20:45:27] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:27] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:27] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":35}
[2025-07-17 20:45:28] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":34,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.941"},"date_expiry":1752796331,"date_start":1752796327,"display_value":"0.35","id":"21bae406-5a3e-48dd-fd79-d799caaa6804","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.941,"spot_time":**********,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":34}
[2025-07-17 20:45:28] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:28] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:28] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":35},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808},{"app_id":82663,"buy_price":0.74,"contract_id":288132698408,"payout":1.44,"purchase_time":1752709333,"sell_price":0,"sell_time":1752709338,"transaction_id":574199594568}]},"req_id":35}
[2025-07-17 20:45:28] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:29] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.052,"bid":2877.852,"epoch":1752796328,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.952,"symbol":"R_25"}}
[2025-07-17 20:45:29] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=21bae406-5a3e-48dd-fd79-d799caaa6804, Price=0.66
[2025-07-17 20:45:29] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"21bae406-5a3e-48dd-fd79-d799caaa6804","price":0.66,"req_id":36}
[2025-07-17 20:45:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:29] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:30] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.07,"buy_price":0.35,"contract_id":288241020928,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796329,"shortcode":"CALL_R_25_0.66_1752796329_2T_S0P_0","start_time":1752796329,"transaction_id":574409867768},"echo_req":{"buy":"21bae406-5a3e-48dd-fd79-d799caaa6804","price":0.66,"req_id":36},"msg_type":"buy","req_id":36}
[2025-07-17 20:45:30] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241020928, BuyPrice=0.35, Payout=0.66, Balance=76289.07
[2025-07-17 20:45:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241020928, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241020928, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241020928
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241020928, ContractType=Higher
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:30] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:30] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":37}
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241020928, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:30] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":37,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.952"},"date_expiry":1752796333,"date_start":1752796329,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.952,"spot_time":1752796328,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":37}
[2025-07-17 20:45:30] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:30] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:30] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:30] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":38}
[2025-07-17 20:45:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":38,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.952"},"date_expiry":1752796334,"date_start":1752796330,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.952,"spot_time":1752796328,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":38}
[2025-07-17 20:45:31] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:23.747. Criando nova simulação às 20:45:31.242.
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:23.747 (expiração da anterior)
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:23.747, Expiração prevista: 20:45:27.747
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204523_4143, Duração=4s
[2025-07-17 20:45:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.064,"bid":2877.864,"epoch":1752796330,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.964,"symbol":"R_25"}}
[2025-07-17 20:45:31] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:31] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":39}
[2025-07-17 20:45:31] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":39,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.964"},"date_expiry":1752796334,"date_start":1752796330,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.964,"spot_time":1752796330,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":39}
[2025-07-17 20:45:31] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:31] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:32] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:33] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.099,"bid":2877.899,"epoch":1752796332,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.999,"symbol":"R_25"}}
[2025-07-17 20:45:33] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:34] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241020928
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2877.952
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2877.999
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241020928
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241020928
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2877.952
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2877.999
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:45:34] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":40}
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:45:34] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:34] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":41}
[2025-07-17 20:45:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":40,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.999"},"date_expiry":**********,"date_start":1752796334,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.999,"spot_time":1752796332,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":40}
[2025-07-17 20:45:35] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.095,"bid":2877.895,"epoch":1752796334,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2877.995,"symbol":"R_25"}}
[2025-07-17 20:45:35] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":41},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":0,"sell_time":1752796329,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.74,"contract_id":288132706908,"payout":1.44,"purchase_time":1752709343,"sell_price":1.44,"sell_time":1752709348,"transaction_id":574199610808}]},"req_id":41}
[2025-07-17 20:45:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:35] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:35] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:35] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":42}
[2025-07-17 20:45:35] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:36] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":42,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.995"},"date_expiry":1752796339,"date_start":1752796335,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.995,"spot_time":1752796334,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":42}
[2025-07-17 20:45:36] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:27.747. Criando nova simulação às 20:45:36.232.
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:27.747 (expiração da anterior)
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:27.747, Expiração prevista: 20:45:31.747
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204527_9252, Duração=4s
[2025-07-17 20:45:36] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:36] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":43}
[2025-07-17 20:45:36] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":43,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2877.995"},"date_expiry":1752796339,"date_start":1752796335,"display_value":"0.35","id":"7e95849f-0d2c-681b-70ae-e3d516410766","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2877.995,"spot_time":1752796334,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":43}
[2025-07-17 20:45:36] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:36] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:37] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.166,"bid":2877.966,"epoch":1752796336,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.066,"symbol":"R_25"}}
[2025-07-17 20:45:37] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=7e95849f-0d2c-681b-70ae-e3d516410766, Price=0.66
[2025-07-17 20:45:37] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"7e95849f-0d2c-681b-70ae-e3d516410766","price":0.66,"req_id":44}
[2025-07-17 20:45:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:37] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:38] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.38,"buy_price":0.35,"contract_id":************,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","start_time":**********,"transaction_id":************},"echo_req":{"buy":"7e95849f-0d2c-681b-70ae-e3d516410766","price":0.66,"req_id":44},"msg_type":"buy","req_id":44}
[2025-07-17 20:45:38] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=************, BuyPrice=0.35, Payout=0.66, Balance=76289.38
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: ************
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=************, ContractType=Higher
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:38] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:38] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":45}
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: ************, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:38] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:38] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":45,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.066"},"date_expiry":**********,"date_start":**********,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.066,"spot_time":1752796336,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":45}
[2025-07-17 20:45:38] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:38] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:38] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":46}
[2025-07-17 20:45:39] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":46,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.066"},"date_expiry":1752796342,"date_start":**********,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.066,"spot_time":1752796336,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":46}
[2025-07-17 20:45:39] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:39] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:39] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.237,"bid":2878.037,"epoch":**********,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.137,"symbol":"R_25"}}
[2025-07-17 20:45:39] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:40] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":47}
[2025-07-17 20:45:40] [Debug] Excalibur.Core.Services.DerivApiService: Requesting contract details for ************
[2025-07-17 20:45:40] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal_open_contract":1,"contract_id":"************","req_id":48}
[2025-07-17 20:45:40] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:40] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":47},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":0,"sell_time":1752796329,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************}]},"req_id":47}
[2025-07-17 20:45:40] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"contract_id":"************","proposal_open_contract":1,"req_id":48},"msg_type":"proposal_open_contract","proposal_open_contract":{"account_id":********,"barrier":"2878.137","barrier_count":1,"bid_price":0.31,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2878.137,"current_spot_display_value":"2878.137","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2878.137,"entry_spot_display_value":"2878.137","entry_tick":2878.137,"entry_tick_display_value":"2878.137","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.04,"profit_percentage":-11.43,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2878.137,"tick_display_value":"2878.137"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"},"req_id":48}
[2025-07-17 20:45:40] [Error] Excalibur.Core.Services.DerivApiService: Erro ao processar contract details: {"account_id":********,"barrier":"2878.137","barrier_count":1,"bid_price":0.31,"buy_price":0.35,"contract_id":************,"contract_type":"CALL","currency":"USD","current_spot":2878.137,"current_spot_display_value":"2878.137","current_spot_time":**********,"date_expiry":**********,"date_settlement":**********,"date_start":**********,"display_name":"Volatility 25 Index","entry_spot":2878.137,"entry_spot_display_value":"2878.137","entry_tick":2878.137,"entry_tick_display_value":"2878.137","entry_tick_time":**********,"expiry_time":**********,"is_expired":0,"is_forward_starting":0,"is_intraday":1,"is_path_dependent":0,"is_settleable":0,"is_sold":0,"is_valid_to_cancel":0,"is_valid_to_sell":0,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"profit":-0.04,"profit_percentage":-11.43,"purchase_time":**********,"shortcode":"CALL_R_25_0.66_**********_2T_S0P_0","status":"open","tick_count":2,"tick_stream":[{"epoch":**********,"tick":2878.137,"tick_display_value":"2878.137"}],"transaction_ids":{"buy":************},"underlying":"R_25","validation_error":"Invalid barrier.","validation_error_code":"General"}
[2025-07-17 20:45:40] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:31.747. Criando nova simulação às 20:45:41.245.
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:31.747 (expiração da anterior)
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:31.747, Expiração prevista: 20:45:35.747
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204531_7591, Duração=4s
[2025-07-17 20:45:41] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.389,"bid":2878.189,"epoch":1752796340,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.289,"symbol":"R_25"}}
[2025-07-17 20:45:41] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:41] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":49}
[2025-07-17 20:45:41] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":49,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.289"},"date_expiry":1752796344,"date_start":1752796340,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.289,"spot_time":1752796340,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":49}
[2025-07-17 20:45:41] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:41] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:42] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: ************
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2878.066
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2878.289
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: ************
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: ************
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2878.066
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2878.289
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base detectada da primeira transação: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake base capturada: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado antes do cálculo:
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - BaseStake: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsLoss: False
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Estado após o cálculo:
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - NextStake: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - ShouldContinueSequence: False
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - IsInMartingaleSequence: False
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] - CurrentMartingaleLevel: 0
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake antes do reset: 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake após reset (calculada): 0.35
[2025-07-17 20:45:42] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":50}
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ✅ Stake atualizada na UI: 0.35 → 0.35
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Vitória - sequência de martingale finalizada
[2025-07-17 20:45:42] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:42] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":51}
[2025-07-17 20:45:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":50,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.289"},"date_expiry":1752796346,"date_start":1752796342,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.289,"spot_time":1752796340,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":50}
[2025-07-17 20:45:43] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:43] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":51},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":0,"sell_time":1752796329,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************}]},"req_id":51}
[2025-07-17 20:45:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:43] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.339,"bid":2878.139,"epoch":1752796342,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.239,"symbol":"R_25"}}
[2025-07-17 20:45:43] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:44] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:45] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.678,"bid":2878.478,"epoch":1752796344,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.578,"symbol":"R_25"}}
[2025-07-17 20:45:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:45] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:45] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:45] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":52}
[2025-07-17 20:45:45] [Debug] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposal throttled - too soon since last request
[2025-07-17 20:45:46] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":52,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.578"},"date_expiry":1752796349,"date_start":1752796345,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.578,"spot_time":1752796344,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":52}
[2025-07-17 20:45:46] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação anterior expirou às 20:45:35.747. Criando nova simulação às 20:45:46.236.
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Nova simulação iniciará às 20:45:35.747 (expiração da anterior)
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Executando simulação automática: ContractType=Higher, Symbol=R_25, Stake=0.35, Duration=2t (4s)
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Tempo de início: 20:45:35.747, Expiração prevista: 20:45:39.747
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Simulação automática criada: ID=SIM_20250717204535_8771, Duração=4s
[2025-07-17 20:45:46] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:46] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":53}
[2025-07-17 20:45:46] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":53,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.578"},"date_expiry":1752796349,"date_start":1752796345,"display_value":"0.35","id":"577f4483-1b7b-9bed-5396-e7689158930b","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.578,"spot_time":1752796344,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":53}
[2025-07-17 20:45:46] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:46] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:47] [Debug] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Ainda há transações ativas na simulação, aguardando...
[2025-07-17 20:45:47] [Information] Excalibur.ViewModels.ProposalViewModel: [SIMULATE] Parando simulação automática
[2025-07-17 20:45:47] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.719,"bid":2878.519,"epoch":1752796346,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.619,"symbol":"R_25"}}
[2025-07-17 20:45:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Executando compra: ProposalId=577f4483-1b7b-9bed-5396-e7689158930b, Price=0.66
[2025-07-17 20:45:47] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"buy":"577f4483-1b7b-9bed-5396-e7689158930b","price":0.66,"req_id":54}
[2025-07-17 20:45:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Solicitação de compra enviada com sucesso
[2025-07-17 20:45:47] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Agendando nova proposta após compra
[2025-07-17 20:45:48] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"buy":{"balance_after":76289.69,"buy_price":0.35,"contract_id":288241040828,"longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"purchase_time":1752796347,"shortcode":"CALL_R_25_0.66_1752796347_2T_S0P_0","start_time":1752796347,"transaction_id":574409907468},"echo_req":{"buy":"577f4483-1b7b-9bed-5396-e7689158930b","price":0.66,"req_id":54},"msg_type":"buy","req_id":54}
[2025-07-17 20:45:48] [Information] Excalibur.Core.Services.DerivApiService: Compra realizada com sucesso: ContractId=288241040828, BuyPrice=0.35, Payout=0.66, Balance=76289.69
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Compra concluída: Compra realizada! ID: 288241040828, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Extraindo Contract ID da mensagem: Compra realizada! ID: 288241040828, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Contract ID extraído: 288241040828
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Disparando evento ContractPurchased: ContractId=288241040828, ContractType=Higher
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Listeners para ContractPurchased: 1
[2025-07-17 20:45:48] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:48] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":55}
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Evento ContractPurchased disparado com sucesso!
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [BUY] Feedback para usuário: Compra realizada! ID: 288241040828, Preço: R$ 0,35, Payout: R$ 0,66
[2025-07-17 20:45:48] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":55,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.619"},"date_expiry":1752796351,"date_start":1752796347,"display_value":"0.35","id":"c50a56e2-7238-e6d4-d8b5-505207e6b97a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.619,"spot_time":1752796346,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":55}
[2025-07-17 20:45:48] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:48] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Solicitando proposta: Symbol=R_25, Contract=CALL, Stake=0.35, Duration=2, Unit=t, Barrier=null
[2025-07-17 20:45:48] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"proposal":1,"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","symbol":"R_25","req_id":56}
[2025-07-17 20:45:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"amount":0.35,"basis":"stake","contract_type":"CALL","currency":"USD","duration":2,"duration_unit":"t","product_type":"basic","proposal":1,"req_id":56,"symbol":"R_25"},"msg_type":"proposal","proposal":{"ask_price":0.35,"contract_details":{"barrier":"2878.619"},"date_expiry":1752796352,"date_start":1752796348,"display_value":"0.35","id":"c50a56e2-7238-e6d4-d8b5-505207e6b97a","longcode":"Win payout if Volatility 25 Index after 2 ticks is strictly higher than entry spot.","payout":0.66,"spot":2878.619,"spot_time":1752796346,"validation_params":{"payout":{"max":"50000.00"},"stake":{"min":"0.35"}}},"req_id":56}
[2025-07-17 20:45:49] [Information] Excalibur.Core.Services.DerivApiService: Proposal received: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:49] [Information] Excalibur.ViewModels.ProposalViewModel: [PROPOSAL-VM] Proposta recebida: Payout=0.66, AskPrice=0.35
[2025-07-17 20:45:49] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.742,"bid":2878.542,"epoch":1752796348,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.642,"symbol":"R_25"}}
[2025-07-17 20:45:51] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.754,"bid":2878.554,"epoch":1752796350,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.654,"symbol":"R_25"}}
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] RefId: 288241040828
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Tipo: Higher
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] StartSpot: 2878.619
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] EndSpot: 2878.654
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Stake: 0.35
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] P/L: 0.31
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] Status: VITÓRIA
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] IsWaitingForResult: True
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] PendingTransactionId: 288241040828
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] ===== PROCESSANDO RESULTADO =====
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] RefId: 288241040828
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Tipo: Higher
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] StartSpot: 2878.619
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] EndSpot: 2878.654
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake: 0.35
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] P/L: 0.31
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Resultado detectado: VITÓRIA
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [MARTINGALE] Stake atual no Proposal: 0.35
[2025-07-17 20:45:52] [Information] Excalibur.Services.AppLoggerService: [CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído
[2025-07-17 20:45:52] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":57}
[2025-07-17 20:45:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":57},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0.66,"sell_time":1752796342,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":0,"sell_time":1752796329,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708},{"app_id":82663,"buy_price":0.35,"contract_id":288195749308,"payout":0.66,"purchase_time":**********,"sell_price":0,"sell_time":**********,"transaction_id":************}]},"req_id":57}
[2025-07-17 20:45:53] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.58,"bid":2878.38,"epoch":1752796352,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.48,"symbol":"R_25"}}
[2025-07-17 20:45:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.697,"bid":2878.497,"epoch":1752796354,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.597,"symbol":"R_25"}}
[2025-07-17 20:45:55] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":58}
[2025-07-17 20:45:55] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"profit_table":1,"req_id":58},"msg_type":"profit_table","profit_table":{"count":50,"transactions":[{"app_id":82663,"buy_price":0.35,"contract_id":288241040828,"payout":0.66,"purchase_time":1752796347,"sell_price":0,"sell_time":1752796352,"transaction_id":574409907468},{"app_id":82663,"buy_price":0.35,"contract_id":************,"payout":0.66,"purchase_time":**********,"sell_price":0.66,"sell_time":1752796342,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241020928,"payout":0.66,"purchase_time":1752796329,"sell_price":0.66,"sell_time":1752796334,"transaction_id":574409867768},{"app_id":82663,"buy_price":0.74,"contract_id":************,"payout":1.44,"purchase_time":**********,"sell_price":0,"sell_time":1752796329,"transaction_id":************},{"app_id":82663,"buy_price":0.35,"contract_id":288241001968,"payout":0.66,"purchase_time":1752796312,"sell_price":0.66,"sell_time":1752796319,"transaction_id":574409829708},{"app_id":82663,"buy_price":0.35,"contract_id":288240990648,"payout":0.66,"purchase_time":1752796301,"sell_price":0.66,"sell_time":1752796306,"transaction_id":574409806148},{"app_id":82663,"buy_price":0.74,"contract_id":288240470408,"payout":1.44,"purchase_time":1752795791,"sell_price":0,"sell_time":1752795796,"transaction_id":574408802168},{"app_id":82663,"buy_price":0.35,"contract_id":288240463828,"payout":0.66,"purchase_time":1752795784,"sell_price":0,"sell_time":1752795790,"transaction_id":574408789028},{"app_id":82663,"buy_price":0.35,"contract_id":288240448708,"payout":0.66,"purchase_time":1752795769,"sell_price":0.66,"sell_time":1752795774,"transaction_id":574408759328},{"app_id":82663,"buy_price":0.35,"contract_id":288238541168,"payout":0.66,"purchase_time":1752793935,"sell_price":0,"sell_time":1752793940,"transaction_id":574405026488},{"app_id":82663,"buy_price":1.54,"contract_id":288238528668,"payout":3.01,"purchase_time":1752793925,"sell_price":3.01,"sell_time":1752793930,"transaction_id":574405006268},{"app_id":82663,"buy_price":0.74,"contract_id":288238518128,"payout":1.44,"purchase_time":1752793915,"sell_price":1.44,"sell_time":1752793920,"transaction_id":574404984468},{"app_id":82663,"buy_price":0.35,"contract_id":288238508668,"payout":0.66,"purchase_time":1752793905,"sell_price":0,"sell_time":1752793910,"transaction_id":574404965768},{"app_id":82663,"buy_price":0.35,"contract_id":288238485148,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404916488},{"app_id":82663,"buy_price":0.35,"contract_id":288238483528,"payout":0.66,"purchase_time":1752793881,"sell_price":0.66,"sell_time":1752793886,"transaction_id":574404914888},{"app_id":82663,"buy_price":0.74,"contract_id":288238470688,"payout":1.44,"purchase_time":1752793870,"sell_price":0,"sell_time":1752793876,"transaction_id":574404894868},{"app_id":82663,"buy_price":0.35,"contract_id":288238459568,"payout":0.66,"purchase_time":1752793860,"sell_price":0,"sell_time":1752793866,"transaction_id":574404872448},{"app_id":82663,"buy_price":0.74,"contract_id":288238448988,"payout":1.44,"purchase_time":1752793850,"sell_price":1.44,"sell_time":1752793856,"transaction_id":574404850868},{"app_id":82663,"buy_price":0.35,"contract_id":288238438348,"payout":0.66,"purchase_time":1752793840,"sell_price":0,"sell_time":1752793846,"transaction_id":574404829468},{"app_id":82663,"buy_price":0.35,"contract_id":288236979948,"payout":0.66,"purchase_time":1752792376,"sell_price":0.66,"sell_time":1752792382,"transaction_id":574401938048},{"app_id":82663,"buy_price":0.35,"contract_id":288236969968,"payout":0.66,"purchase_time":1752792366,"sell_price":0.66,"sell_time":1752792372,"transaction_id":574401918288},{"app_id":82663,"buy_price":3.24,"contract_id":288236960968,"payout":6.33,"purchase_time":1752792358,"sell_price":6.33,"sell_time":1752792364,"transaction_id":574401900668},{"app_id":82663,"buy_price":1.54,"contract_id":288236954208,"payout":3.01,"purchase_time":1752792351,"sell_price":3.01,"sell_time":1752792356,"transaction_id":574401886528},{"app_id":82663,"buy_price":0.74,"contract_id":288236944388,"payout":1.44,"purchase_time":1752792341,"sell_price":0,"sell_time":1752792346,"transaction_id":574401867208},{"app_id":82663,"buy_price":0.35,"contract_id":288236935248,"payout":0.66,"purchase_time":1752792331,"sell_price":0,"sell_time":1752792336,"transaction_id":574401849048},{"app_id":82663,"buy_price":0.35,"contract_id":288236925968,"payout":0.66,"purchase_time":1752792321,"sell_price":0.66,"sell_time":1752792326,"transaction_id":574401830848},{"app_id":82663,"buy_price":0.35,"contract_id":288236918748,"payout":0.66,"purchase_time":1752792313,"sell_price":0.66,"sell_time":1752792318,"transaction_id":574401816128},{"app_id":82663,"buy_price":0.74,"contract_id":288236912028,"payout":1.44,"purchase_time":1752792306,"sell_price":1.44,"sell_time":1752792312,"transaction_id":574401803008},{"app_id":82663,"buy_price":0.35,"contract_id":288236900608,"payout":0.66,"purchase_time":1752792296,"sell_price":0.66,"sell_time":1752792303,"transaction_id":574401779788},{"app_id":82663,"buy_price":1.54,"contract_id":288236891608,"payout":3.01,"purchase_time":1752792286,"sell_price":3.01,"sell_time":1752792292,"transaction_id":574401762328},{"app_id":82663,"buy_price":0.74,"contract_id":288236882788,"payout":1.44,"purchase_time":1752792276,"sell_price":0,"sell_time":1752792282,"transaction_id":574401744648},{"app_id":82663,"buy_price":0.35,"contract_id":288236873928,"payout":0.66,"purchase_time":1752792266,"sell_price":0,"sell_time":1752792272,"transaction_id":574401727008},{"app_id":82663,"buy_price":0.35,"contract_id":288236855608,"payout":0.66,"purchase_time":1752792246,"sell_price":0,"sell_time":1752792252,"transaction_id":574401691048},{"app_id":82663,"buy_price":1.54,"contract_id":288236847988,"payout":3.01,"purchase_time":1752792238,"sell_price":3.01,"sell_time":1752792244,"transaction_id":574401675488},{"app_id":82663,"buy_price":0.74,"contract_id":288236841688,"payout":1.44,"purchase_time":1752792231,"sell_price":0,"sell_time":1752792236,"transaction_id":574401662688},{"app_id":82663,"buy_price":0.35,"contract_id":288236833228,"payout":0.66,"purchase_time":1752792221,"sell_price":0,"sell_time":1752792226,"transaction_id":574401645248},{"app_id":82663,"buy_price":1.54,"contract_id":288236824608,"payout":3.01,"purchase_time":1752792211,"sell_price":3.01,"sell_time":1752792216,"transaction_id":574401628308},{"app_id":82663,"buy_price":0.74,"contract_id":288236816008,"payout":1.44,"purchase_time":1752792201,"sell_price":0,"sell_time":1752792206,"transaction_id":574401611608},{"app_id":82663,"buy_price":0.35,"contract_id":288236807448,"payout":0.66,"purchase_time":1752792191,"sell_price":0,"sell_time":1752792196,"transaction_id":574401593328},{"app_id":82663,"buy_price":0.35,"contract_id":288236787108,"payout":0.66,"purchase_time":1752792171,"sell_price":0.66,"sell_time":1752792176,"transaction_id":574401553308},{"app_id":82663,"buy_price":0.35,"contract_id":288236777308,"payout":0.66,"purchase_time":1752792161,"sell_price":0.66,"sell_time":1752792166,"transaction_id":574401534108},{"app_id":82663,"buy_price":0.35,"contract_id":288236758508,"payout":0.66,"purchase_time":1752792141,"sell_price":0.66,"sell_time":1752792146,"transaction_id":574401497088},{"app_id":82663,"buy_price":0.35,"contract_id":288236749028,"payout":0.66,"purchase_time":1752792131,"sell_price":0.66,"sell_time":1752792136,"transaction_id":574401477748},{"app_id":82663,"buy_price":0.35,"contract_id":288197656228,"payout":0.66,"purchase_time":1752764521,"sell_price":0,"sell_time":1752764526,"transaction_id":574325005488},{"app_id":82663,"buy_price":0.74,"contract_id":288197630928,"payout":1.44,"purchase_time":1752764506,"sell_price":0,"sell_time":1752764512,"transaction_id":574324957048},{"app_id":82663,"buy_price":0.35,"contract_id":288197615868,"payout":0.66,"purchase_time":1752764496,"sell_price":0,"sell_time":1752764502,"transaction_id":574324927948},{"app_id":82663,"buy_price":0.35,"contract_id":288197601808,"payout":0.66,"purchase_time":1752764486,"sell_price":0.66,"sell_time":1752764492,"transaction_id":574324900108},{"app_id":82663,"buy_price":0.74,"contract_id":288197303388,"payout":1.44,"purchase_time":1752764290,"sell_price":0,"sell_time":1752764297,"transaction_id":574324309328},{"app_id":82663,"buy_price":0.35,"contract_id":288197287328,"payout":0.66,"purchase_time":1752764280,"sell_price":0,"sell_time":1752764287,"transaction_id":574324277248},{"app_id":82663,"buy_price":0.74,"contract_id":288195765008,"payout":1.44,"purchase_time":1752763307,"sell_price":1.44,"sell_time":1752763312,"transaction_id":574321261708}]},"req_id":58}
[2025-07-17 20:45:57] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.496,"bid":2878.296,"epoch":1752796356,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.396,"symbol":"R_25"}}
[2025-07-17 20:45:59] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.55,"bid":2878.35,"epoch":1752796358,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.45,"symbol":"R_25"}}
[2025-07-17 20:46:01] [Debug] Excalibur.Core.Services.DerivApiService: Recebido: {"echo_req":{"req_id":5,"subscribe":1,"ticks":"R_25"},"msg_type":"tick","req_id":5,"subscription":{"id":"8ac2498c-b556-71ce-6fda-b0523311be4d"},"tick":{"ask":2878.577,"bid":2878.377,"epoch":1752796360,"id":"8ac2498c-b556-71ce-6fda-b0523311be4d","pip_size":3,"quote":2878.477,"symbol":"R_25"}}
[2025-07-17 20:46:01] [Information] Excalibur.Services.AppLoggerService: Conectando evento ContractSimulated...
[2025-07-17 20:46:01] [Information] Excalibur.Services.AppLoggerService: Evento ContractSimulated conectado com sucesso!
[2025-07-17 20:46:01] [Information] Excalibur.Services.AppLoggerService: Configurando simulação inicial: Amostra=1, Win=1, Loss=0
[2025-07-17 20:46:01] [Debug] Excalibur.Core.Services.DerivApiService: Enviado: {"profit_table":1,"req_id":59}
[2025-07-17 20:46:01] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:46:01] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações carregadas com sucesso
[2025-07-17 20:46:01] [Information] Excalibur.Services.SettingsService: [SETTINGS] Configurações salvas com sucesso
[2025-07-17 20:46:01] [Error] Excalibur.Core.Services.DerivApiService: Erro ao receber mensagem
