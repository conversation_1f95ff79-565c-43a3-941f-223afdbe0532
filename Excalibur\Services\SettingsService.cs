using System.IO;
using System.Text.Json;
using Excalibur.Models;
using Microsoft.Extensions.Logging;

namespace Excalibur.Services;

public interface ISettingsService
{
    Task<AppSettings> LoadSettingsAsync();
    Task SaveSettingsAsync(AppSettings settings);
    Task SaveProposalSettingsAsync(ProposalSettings settings);
    Task SaveMoneyManagementSettingsAsync(MoneyManagementSettings settings);
}

public class SettingsService : ISettingsService
{
    private readonly ILogger<SettingsService> _logger;
    private readonly string _settingsFilePath;
    private readonly JsonSerializerOptions _jsonOptions;

    public SettingsService(ILogger<SettingsService> logger)
    {
        _logger = logger;
        _settingsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Excalibur", "settings.json");
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // Criar diretório se não existir
        var directory = Path.GetDirectoryName(_settingsFilePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
    }

    public async Task<AppSettings> LoadSettingsAsync()
    {
        try
        {
            if (!File.Exists(_settingsFilePath))
            {
                _logger.LogInformation("[SETTINGS] Arquivo de configurações não encontrado, usando padrões");
                return new AppSettings();
            }

            var json = await File.ReadAllTextAsync(_settingsFilePath);
            var settings = JsonSerializer.Deserialize<AppSettings>(json, _jsonOptions);
            
            _logger.LogInformation("[SETTINGS] Configurações carregadas com sucesso");
            return settings ?? new AppSettings();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SETTINGS] Erro ao carregar configurações, usando padrões");
            return new AppSettings();
        }
    }

    public async Task SaveSettingsAsync(AppSettings settings)
    {
        try
        {
            var json = JsonSerializer.Serialize(settings, _jsonOptions);
            await File.WriteAllTextAsync(_settingsFilePath, json);
            
            _logger.LogInformation("[SETTINGS] Configurações salvas com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SETTINGS] Erro ao salvar configurações");
        }
    }

    public async Task SaveProposalSettingsAsync(ProposalSettings settings)
    {
        var appSettings = await LoadSettingsAsync();
        appSettings.Proposal = settings;
        appSettings.SaveProposalSettings = true;
        await SaveSettingsAsync(appSettings);
    }

    public async Task SaveMoneyManagementSettingsAsync(MoneyManagementSettings settings)
    {
        var appSettings = await LoadSettingsAsync();
        appSettings.MoneyManagement = settings;
        appSettings.SaveMoneyManagementSettings = true;
        await SaveSettingsAsync(appSettings);
    }
}
