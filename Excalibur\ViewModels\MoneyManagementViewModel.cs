using System.Globalization;
using Excalibur.Services;
using Excalibur.Models;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class MoneyManagementViewModel : BaseViewModel
{
   private decimal _baseStake = 0.35m; // Stake base capturada do usuário
    
    public decimal BaseStake 
    { 
        get => _baseStake; 
        private set 
        { 
            _baseStake = value; 
            OnPropertyChanged(); 
        } 
    }
    
    // Método para capturar stake base no início das operações
    public void SetBaseStake(decimal stake)
    {
        BaseStake = stake;
        System.Diagnostics.Debug.WriteLine($"[MONEY-MANAGEMENT] Stake base capturada: {BaseStake:F2}");
    }
    
    // Modificar GetNextStake para usar BaseStake
    public (decimal nextStake, bool shouldContinue) GetNextStake(bool isLoss)
    {
        return _martingaleService.CalculateNextStake(BaseStake, isLoss, Factor, Level);
    }
    private readonly IMartingaleService _martingaleService;
    private readonly ISettingsService _settingsService;
    private readonly ILogger<MoneyManagementViewModel> _logger;
    
    public MoneyManagementViewModel(IMartingaleService martingaleService, ISettingsService settingsService, ILogger<MoneyManagementViewModel> logger)
    {
        _martingaleService = martingaleService;
        _settingsService = settingsService;
        _logger = logger;
        
        // Carregar configurações se disponível
        _ = LoadSettingsAsync();
    }

    private string _factorText = "2";
    public string FactorText
    {
        get => _factorText;
        set
        {
            if (_factorText != value)
            {
                _factorText = value;
                OnPropertyChanged();
            }
        }
    }

    private string _levelText = "1";
    public string LevelText
    {
        get => _levelText;
        set
        {
            if (_levelText != value)
            {
                _levelText = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _martingaleEnabled = true;
    public bool MartingaleEnabled
    {
        get => _martingaleEnabled;
        set
        {
            if (_martingaleEnabled != value)
            {
                _martingaleEnabled = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _saveSettings = false;
    public bool SaveSettings
    {
        get => _saveSettings;
        set
        {
            if (_saveSettings != value)
            {
                _saveSettings = value;
                OnPropertyChanged();
                
                if (_saveSettings)
                {
                    _ = SaveCurrentSettingsAsync();
                }
            }
        }
    }

    // Properties for martingale state access
    public bool IsInMartingaleSequence => _martingaleService.IsInMartingaleSequence;
    public int CurrentMartingaleLevel => _martingaleService.CurrentLevel;

    // Novos campos: Amostra, Win e Loss
    private string _amostraText = "5";
    public string AmostraText
    {
        get => _amostraText;
        set
        {
            if (_amostraText != value)
            {
                _amostraText = value;
                OnPropertyChanged();
                UpdateLossFromAmostraAndWin(); // Atualizar Loss quando Amostra muda
            }
        }
    }

    private string _winText = "2";
    public string WinText
    {
        get => _winText;
        set
        {
            if (_winText != value)
            {
                _winText = value;
                OnPropertyChanged();
                UpdateLossFromAmostraAndWin(); // Atualizar Loss quando Win muda
            }
        }
    }

    private string _lossText = "3";
    public string LossText
    {
        get => _lossText;
        set
        {
            if (_lossText != value)
            {
                _lossText = value;
                OnPropertyChanged();
                UpdateWinFromAmostraAndLoss(); // Atualizar Win quando Loss muda
            }
        }
    }

    private bool _isUpdating = false; // Flag para evitar loops infinitos

    // Métodos para cálculo automático
    private void UpdateLossFromAmostraAndWin()
    {
        if (_isUpdating) return; // Evitar loops infinitos
        
        _isUpdating = true;
        try
        {
            if (int.TryParse(_amostraText, out int amostra) && int.TryParse(_winText, out int win))
            {
                // Garantir que os valores sejam não-negativos
                amostra = Math.Max(0, amostra);
                win = Math.Max(0, Math.Min(win, amostra)); // Win não pode ser maior que Amostra
                
                int calculatedLoss = amostra - win;
                _lossText = calculatedLoss.ToString();
                OnPropertyChanged(nameof(LossText));
                
                // Se Win foi ajustado, atualizar também
                if (win != int.Parse(_winText))
                {
                    _winText = win.ToString();
                    OnPropertyChanged(nameof(WinText));
                }
            }
        }
        finally
        {
            _isUpdating = false;
        }
    }

    private void UpdateWinFromAmostraAndLoss()
    {
        if (_isUpdating) return; // Evitar loops infinitos
        
        _isUpdating = true;
        try
        {
            if (int.TryParse(_amostraText, out int amostra) && int.TryParse(_lossText, out int loss))
            {
                // Garantir que os valores sejam não-negativos
                amostra = Math.Max(0, amostra);
                loss = Math.Max(0, Math.Min(loss, amostra)); // Loss não pode ser maior que Amostra
                
                int calculatedWin = amostra - loss;
                _winText = calculatedWin.ToString();
                OnPropertyChanged(nameof(WinText));
                
                // Se Loss foi ajustado, atualizar também
                if (loss != int.Parse(_lossText))
                {
                    _lossText = loss.ToString();
                    OnPropertyChanged(nameof(LossText));
                }
            }
        }
        finally
        {
            _isUpdating = false;
        }
    }

    // Conveniência para obter valores numéricos (parse seguro)
    public decimal Factor
    {
        get => decimal.TryParse(_factorText, NumberStyles.Any, CultureInfo.InvariantCulture, out var v) ? v : 1m;
    }
    
    public int Level
    {
        get => int.TryParse(_levelText, out var v) ? v : 1;
    }

    public int Amostra
    {
        get => int.TryParse(_amostraText, out var v) ? v : 5;
    }

    public int Win
    {
        get => int.TryParse(_winText, out var v) ? v : 2;
    }

    public int Loss
    {
        get => int.TryParse(_lossText, out var v) ? v : 3;
    }

    // Métodos para controle de martingale
    public (decimal nextStake, bool shouldContinueSequence) GetNextStake(decimal originalStake, bool isLoss)
    {
        if (!MartingaleEnabled)
        {
            _martingaleService.ResetMartingale();
            return (originalStake, false);
        }

        return _martingaleService.CalculateNextStake(originalStake, isLoss, Factor, Level);
    }

    public void ResetMartingale()
    {
        _martingaleService.ResetMartingale();
    }

    // Métodos de persistência de configurações
    private async Task LoadSettingsAsync()
    {
        try
        {
            var settings = await _settingsService.LoadSettingsAsync();
            
            if (settings.SaveMoneyManagementSettings && settings.MoneyManagement != null)
            {
                _logger.LogInformation("[MM-SETTINGS] Carregando configurações salvas");
                
                // Carregar valores salvos
                FactorText = settings.MoneyManagement.FactorText;
                LevelText = settings.MoneyManagement.LevelText;
                MartingaleEnabled = settings.MoneyManagement.MartingaleEnabled;
                AmostraText = settings.MoneyManagement.AmostraText;
                WinText = settings.MoneyManagement.WinText;
                LossText = settings.MoneyManagement.LossText;
                _saveSettings = true;
                
                OnPropertyChanged(nameof(SaveSettings));
                
                _logger.LogInformation("[MM-SETTINGS] Configurações carregadas: Factor={Factor}, Level={Level}, Enabled={Enabled}", 
                    FactorText, LevelText, MartingaleEnabled);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[MM-SETTINGS] Erro ao carregar configurações");
        }
    }

    private async Task SaveCurrentSettingsAsync()
    {
        try
        {
            var settings = new MoneyManagementSettings
            {
                FactorText = FactorText,
                LevelText = LevelText,
                MartingaleEnabled = MartingaleEnabled,
                AmostraText = AmostraText,
                WinText = WinText,
                LossText = LossText
            };

            await _settingsService.SaveMoneyManagementSettingsAsync(settings);
            
            _logger.LogInformation("[MM-SETTINGS] Configurações salvas: Factor={Factor}, Level={Level}, Enabled={Enabled}", 
                FactorText, LevelText, MartingaleEnabled);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[MM-SETTINGS] Erro ao salvar configurações");
        }
    }
}