using System.Collections.Concurrent;

namespace Excalibur.Services;

public interface IThrottleService
{
    Task<T> ThrottleAsync<T>(string key, Func<Task<T>> operation, TimeSpan throttleInterval);
    bool CanExecute(string key, TimeSpan throttleInterval);
    void Reset(string key);
}

public class ThrottleService : IThrottleService
{
    private readonly ConcurrentDictionary<string, DateTime> _lastExecutionTimes = new();
    private readonly SemaphoreSlim _semaphore = new(1, 1);

    public async Task<T> ThrottleAsync<T>(string key, Func<Task<T>> operation, TimeSpan throttleInterval)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (!CanExecute(key, throttleInterval))
            {
                throw new InvalidOperationException($"Operation '{key}' is throttled. Please wait {throttleInterval.TotalMilliseconds}ms between calls.");
            }

            _lastExecutionTimes[key] = DateTime.UtcNow;
            return await operation();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public bool CanExecute(string key, TimeSpan throttleInterval)
    {
        if (!_lastExecutionTimes.TryGetValue(key, out var lastExecution))
            return true;

        var timeSinceLastExecution = DateTime.UtcNow - lastExecution;
        return timeSinceLastExecution >= throttleInterval;
    }

    public void Reset(string key)
    {
        _lastExecutionTimes.TryRemove(key, out _);
    }
}