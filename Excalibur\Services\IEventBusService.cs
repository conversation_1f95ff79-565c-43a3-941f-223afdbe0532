using System.Collections.Concurrent;

namespace Excalibur.Services;

public interface IEventBusService
{
    Task PublishAsync<T>(T message, CancellationToken cancellationToken = default) where T : class;
    void Subscribe<T>(IEventHandler<T> handler) where T : class;
    void Unsubscribe<T>(IEventHandler<T> handler) where T : class;
}

public interface IEventHandler<in T> where T : class
{
    Task HandleAsync(T message, CancellationToken cancellationToken = default);
}

public class EventBusService : IEventBusService
{
    private readonly ConcurrentDictionary<Type, ConcurrentBag<object>> _handlers = new();

    public void Subscribe<T>(IEventHandler<T> handler) where T : class
    {
        var messageType = typeof(T);
        _handlers.AddOrUpdate(messageType, 
            new ConcurrentBag<object> { handler },
            (key, existing) => 
            {
                existing.Add(handler);
                return existing;
            });
    }

    public void Unsubscribe<T>(IEventHandler<T> handler) where T : class
    {
        var messageType = typeof(T);
        if (_handlers.TryGetValue(messageType, out var handlers))
        {
            var handlersList = handlers.ToList();
            handlersList.Remove(handler);
            _handlers.TryUpdate(messageType, new ConcurrentBag<object>(handlersList), handlers);
        }
    }

    public async Task PublishAsync<T>(T message, CancellationToken cancellationToken = default) where T : class
    {
        var messageType = typeof(T);
        if (!_handlers.TryGetValue(messageType, out var handlers))
            return;

        var tasks = handlers
            .OfType<IEventHandler<T>>()
            .Select(handler => handler.HandleAsync(message, cancellationToken));

        await Task.WhenAll(tasks);
    }
}

// Event messages
public record ContractPurchasedEvent(string ContractId, decimal Stake, DateTime PurchaseTime);
public record ContractExpiredEvent(string ContractId, bool IsWin, decimal ProfitLoss);
public record SimulationCompletedEvent(int WinCount, int LossCount, bool ShouldTriggerPurchase);
public record SettingsChangedEvent(string SettingsType, object Settings);