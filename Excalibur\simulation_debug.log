21:05:36.433 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:05:41.439 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:05:46.444 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:05:51.445 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:05:56.438 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:06:01.440 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:06:43.610 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:06:43.614 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:06:43.615 - EXECUTE REAL PURCHASE: Started
21:06:53.613 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:00.632 - EXECUTE REAL PURCHASE: Started
21:07:08.610 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:17.638 - EXECUTE REAL PURCHASE: Started
21:07:18.603 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:24.628 - EXECUTE REAL PURCHASE: Started
21:07:33.617 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:41.628 - EXECUTE REAL PURCHASE: Started
21:07:43.613 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:43.615 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:07:43.616 - EXECUTE REAL PURCHASE: Started
21:07:53.614 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:07:53.621 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:07:53.622 - EXECUTE REAL PURCHASE: Started
21:08:03.615 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:08:10.656 - EXECUTE REAL PURCHASE: Started
21:08:18.619 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:08:18.623 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:08:18.624 - EXECUTE REAL PURCHASE: Started
21:08:28.606 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:08:35.672 - EXECUTE REAL PURCHASE: Started
21:08:43.606 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:08:43.614 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:08:43.615 - EXECUTE REAL PURCHASE: Started
21:08:53.602 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:00.666 - EXECUTE REAL PURCHASE: Started
21:09:08.609 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:08.616 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:08.616 - EXECUTE REAL PURCHASE: Started
21:09:18.610 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:18.621 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:18.621 - EXECUTE REAL PURCHASE: Started
21:09:28.615 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:28.622 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:28.623 - EXECUTE REAL PURCHASE: Started
21:09:38.616 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:38.620 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:38.621 - EXECUTE REAL PURCHASE: Started
21:09:48.610 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:48.613 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:48.614 - EXECUTE REAL PURCHASE: Started
21:09:58.607 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:09:58.614 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:09:58.614 - EXECUTE REAL PURCHASE: Started
21:10:08.613 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:10:08.617 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:10:08.617 - EXECUTE REAL PURCHASE: Started
21:10:18.619 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:10:25.630 - EXECUTE REAL PURCHASE: Started
21:19:11.331 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:11.332 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:11.332 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:11.333 - Análise da amostra: Wins=1/1, Losses=0/0
21:19:11.333 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:19:11.334 - Verificando se pode disparar compra real...
21:19:11.334 - Estado das tabelas: Compras=0, Simulação=1
21:19:11.335 - REAL PURCHASE STAKE: Primeira compra - Stake=0,35
21:19:11.335 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:19:11.336 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:19:11.340 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:19:11.341 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:19:11.341 - EXECUTE REAL PURCHASE: Started
21:19:11.342 - Compra real disparada com sucesso!
21:19:17.331 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:17.331 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:17.331 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:17.332 - Análise da amostra: Wins=1/1, Losses=0/0
21:19:17.332 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:19:17.332 - Verificando se pode disparar compra real...
21:19:17.333 - Estado das tabelas: Compras=1, Simulação=2
21:19:17.333 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:19:22.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:22.339 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:22.339 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:22.339 - Análise da amostra: Wins=0/1, Losses=1/0
21:19:22.340 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:19:22.340 - Verificando se pode disparar compra real...
21:19:22.340 - Estado das tabelas: Compras=1, Simulação=3
21:19:22.341 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:19:22.341 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:19:22.342 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:19:22.342 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:19:22.342 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:19:22.344 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:19:22.345 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:19:22.345 - EXECUTE REAL PURCHASE: Started
21:19:22.345 - Compra real disparada com sucesso!
21:19:27.324 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:27.324 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:27.325 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:27.325 - Análise da amostra: Wins=0/1, Losses=1/0
21:19:27.325 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:19:27.326 - Verificando se pode disparar compra real...
21:19:27.326 - Estado das tabelas: Compras=2, Simulação=4
21:19:27.327 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:19:32.341 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:32.342 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:32.343 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:32.343 - Análise da amostra: Wins=0/1, Losses=1/0
21:19:32.343 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:19:32.345 - Verificando se pode disparar compra real...
21:19:32.346 - Estado das tabelas: Compras=2, Simulação=5
21:19:32.347 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:19:32.348 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:19:32.349 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:19:32.351 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:19:32.352 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:19:32.357 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:19:32.360 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:19:32.360 - EXECUTE REAL PURCHASE: Started
21:19:32.361 - Compra real disparada com sucesso!
21:19:37.335 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:37.336 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:37.336 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:37.337 - Análise da amostra: Wins=1/1, Losses=0/0
21:19:37.337 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:19:37.337 - Verificando se pode disparar compra real...
21:19:37.337 - Estado das tabelas: Compras=3, Simulação=6
21:19:37.338 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:19:42.337 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:42.337 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:42.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:42.338 - Análise da amostra: Wins=1/1, Losses=0/0
21:19:42.338 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:19:42.338 - Verificando se pode disparar compra real...
21:19:42.339 - Estado das tabelas: Compras=3, Simulação=7
21:19:42.339 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:19:42.339 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:19:42.339 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:19:42.340 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:19:42.340 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:19:42.342 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:19:42.342 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:19:42.342 - EXECUTE REAL PURCHASE: Started
21:19:42.343 - Compra real disparada com sucesso!
21:19:47.337 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:47.337 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:47.337 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:47.338 - Análise da amostra: Wins=1/1, Losses=0/0
21:19:47.338 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:19:47.338 - Verificando se pode disparar compra real...
21:19:47.338 - Estado das tabelas: Compras=4, Simulação=8
21:19:47.339 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:19:52.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:52.339 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:52.339 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:52.339 - Análise da amostra: Wins=0/1, Losses=1/0
21:19:52.339 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:19:52.340 - Verificando se pode disparar compra real...
21:19:52.340 - Estado das tabelas: Compras=4, Simulação=9
21:19:52.340 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:19:52.340 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:19:52.340 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:19:52.340 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:19:52.341 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:19:52.342 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:19:52.343 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:19:52.343 - EXECUTE REAL PURCHASE: Started
21:19:52.343 - Compra real disparada com sucesso!
21:19:57.324 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:19:57.325 - MÉTODO EXECUTADO - TESTE DE LOG
21:19:57.325 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:19:57.325 - Análise da amostra: Wins=0/1, Losses=1/0
21:19:57.325 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:19:57.326 - Verificando se pode disparar compra real...
21:19:57.326 - Estado das tabelas: Compras=5, Simulação=10
21:19:57.326 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:02.340 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:02.341 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:02.341 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:02.341 - Análise da amostra: Wins=0/1, Losses=1/0
21:20:02.341 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:20:02.342 - Verificando se pode disparar compra real...
21:20:02.342 - Estado das tabelas: Compras=5, Simulação=11
21:20:02.344 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:20:02.346 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:20:02.347 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:20:02.347 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:20:02.347 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:20:02.350 - Compra real disparada com sucesso!
21:20:07.328 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:07.328 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:07.329 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:07.329 - Análise da amostra: Wins=0/1, Losses=1/0
21:20:07.329 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:20:07.330 - Verificando se pode disparar compra real...
21:20:07.330 - Estado das tabelas: Compras=6, Simulação=12
21:20:07.330 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:09.384 - EXECUTE REAL PURCHASE: Started
21:20:12.333 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:12.333 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:12.333 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:12.334 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:12.334 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:12.335 - Verificando se pode disparar compra real...
21:20:12.335 - Estado das tabelas: Compras=7, Simulação=13
21:20:12.335 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:17.332 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:17.332 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:17.333 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:17.335 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:17.336 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:17.336 - Verificando se pode disparar compra real...
21:20:17.337 - Estado das tabelas: Compras=7, Simulação=14
21:20:17.337 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=1,47
21:20:17.337 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:20:17.337 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:20:17.338 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:20:17.338 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:20:17.340 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:20:17.340 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:20:17.340 - EXECUTE REAL PURCHASE: Started
21:20:17.340 - Compra real disparada com sucesso!
21:20:22.329 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:22.330 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:22.330 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:22.331 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:22.331 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:22.331 - Verificando se pode disparar compra real...
21:20:22.332 - Estado das tabelas: Compras=8, Simulação=15
21:20:22.332 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:27.328 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:27.328 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:27.329 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:27.329 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:27.329 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:27.329 - Verificando se pode disparar compra real...
21:20:27.330 - Estado das tabelas: Compras=8, Simulação=16
21:20:27.330 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:20:27.330 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:20:27.331 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:20:27.331 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:20:27.331 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:20:27.333 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:20:27.333 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:20:27.333 - EXECUTE REAL PURCHASE: Started
21:20:27.333 - Compra real disparada com sucesso!
21:20:32.331 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:32.331 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:32.331 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:32.332 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:32.332 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:32.332 - Verificando se pode disparar compra real...
21:20:32.332 - Estado das tabelas: Compras=9, Simulação=17
21:20:32.333 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:37.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:37.339 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:37.339 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:37.339 - Análise da amostra: Wins=0/1, Losses=1/0
21:20:37.340 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:20:37.340 - Verificando se pode disparar compra real...
21:20:37.340 - Estado das tabelas: Compras=9, Simulação=18
21:20:37.341 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:20:37.341 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:20:37.341 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:20:37.342 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:20:37.342 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:20:37.346 - Compra real disparada com sucesso!
21:20:42.331 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:42.331 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:42.331 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:42.331 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:42.332 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:42.332 - Verificando se pode disparar compra real...
21:20:42.332 - Estado das tabelas: Compras=10, Simulação=19
21:20:42.333 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:44.387 - EXECUTE REAL PURCHASE: Started
21:20:47.337 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:47.337 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:47.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:47.338 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:47.338 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:47.339 - Verificando se pode disparar compra real...
21:20:47.339 - Estado das tabelas: Compras=11, Simulação=20
21:20:47.339 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:20:52.334 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:52.334 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:52.335 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:52.335 - Análise da amostra: Wins=0/1, Losses=1/0
21:20:52.335 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:20:52.335 - Verificando se pode disparar compra real...
21:20:52.335 - Estado das tabelas: Compras=11, Simulação=21
21:20:52.335 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:20:52.335 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:20:52.336 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:20:52.336 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:20:52.336 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:20:52.338 - Compra real disparada com sucesso!
21:20:57.324 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:20:57.324 - MÉTODO EXECUTADO - TESTE DE LOG
21:20:57.324 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:20:57.325 - Análise da amostra: Wins=1/1, Losses=0/0
21:20:57.325 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:20:57.327 - Verificando se pode disparar compra real...
21:20:57.327 - Estado das tabelas: Compras=12, Simulação=22
21:20:57.327 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:01.387 - EXECUTE REAL PURCHASE: Started
21:21:02.335 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:02.336 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:02.336 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:02.337 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:02.337 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:02.339 - Verificando se pode disparar compra real...
21:21:02.339 - Estado das tabelas: Compras=12, Simulação=23
21:21:02.340 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,70
21:21:02.340 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:21:02.340 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:21:02.340 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:21:02.340 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:21:02.342 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:21:02.343 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:21:02.343 - EXECUTE REAL PURCHASE: Started
21:21:02.343 - Compra real disparada com sucesso!
21:21:07.325 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:07.325 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:07.326 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:07.326 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:07.326 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:07.326 - Verificando se pode disparar compra real...
21:21:07.326 - Estado das tabelas: Compras=14, Simulação=24
21:21:07.327 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:12.337 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:12.338 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:12.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:12.338 - Análise da amostra: Wins=0/1, Losses=1/0
21:21:12.338 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:21:12.338 - Verificando se pode disparar compra real...
21:21:12.339 - Estado das tabelas: Compras=14, Simulação=25
21:21:12.339 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:21:12.340 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:21:12.340 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:21:12.340 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:21:12.340 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:21:12.342 - Compra real disparada com sucesso!
21:21:17.335 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:17.335 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:17.335 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:17.336 - Análise da amostra: Wins=0/1, Losses=1/0
21:21:17.336 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:21:17.336 - Verificando se pode disparar compra real...
21:21:17.336 - Estado das tabelas: Compras=15, Simulação=26
21:21:17.337 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:19.391 - EXECUTE REAL PURCHASE: Started
21:21:22.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:22.338 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:22.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:22.339 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:22.339 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:22.339 - Verificando se pode disparar compra real...
21:21:22.340 - Estado das tabelas: Compras=16, Simulação=27
21:21:22.340 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:27.337 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:27.337 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:27.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:27.338 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:27.338 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:27.339 - Verificando se pode disparar compra real...
21:21:27.339 - Estado das tabelas: Compras=16, Simulação=28
21:21:27.339 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:21:27.340 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:21:27.340 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:21:27.340 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:21:27.341 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:21:27.342 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:21:27.343 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:21:27.343 - EXECUTE REAL PURCHASE: Started
21:21:27.343 - Compra real disparada com sucesso!
21:21:32.333 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:32.334 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:32.334 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:32.334 - Análise da amostra: Wins=0/1, Losses=1/0
21:21:32.334 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:21:32.334 - Verificando se pode disparar compra real...
21:21:32.335 - Estado das tabelas: Compras=17, Simulação=29
21:21:32.335 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:37.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:37.339 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:37.339 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:37.339 - Análise da amostra: Wins=0/1, Losses=1/0
21:21:37.340 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:21:37.340 - Verificando se pode disparar compra real...
21:21:37.340 - Estado das tabelas: Compras=17, Simulação=30
21:21:37.341 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:21:37.341 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:21:37.341 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:21:37.341 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:21:37.341 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:21:37.344 - Compra real disparada com sucesso!
21:21:42.335 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:42.335 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:42.335 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:42.336 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:42.336 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:42.336 - Verificando se pode disparar compra real...
21:21:42.336 - Estado das tabelas: Compras=18, Simulação=31
21:21:42.336 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:44.383 - EXECUTE REAL PURCHASE: Started
21:21:47.325 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:47.326 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:47.326 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:47.326 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:47.327 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:47.327 - Verificando se pode disparar compra real...
21:21:47.327 - Estado das tabelas: Compras=19, Simulação=32
21:21:47.327 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:21:52.328 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:52.328 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:52.329 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:52.329 - Análise da amostra: Wins=1/1, Losses=0/0
21:21:52.329 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:21:52.329 - Verificando se pode disparar compra real...
21:21:52.330 - Estado das tabelas: Compras=19, Simulação=33
21:21:52.330 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:21:52.330 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:21:52.330 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:21:52.331 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:21:52.331 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:21:52.332 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:21:52.333 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:21:52.333 - EXECUTE REAL PURCHASE: Started
21:21:52.333 - Compra real disparada com sucesso!
21:21:57.332 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:21:57.332 - MÉTODO EXECUTADO - TESTE DE LOG
21:21:57.333 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:21:57.333 - Análise da amostra: Wins=0/1, Losses=1/0
21:21:57.333 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:21:57.334 - Verificando se pode disparar compra real...
21:21:57.334 - Estado das tabelas: Compras=20, Simulação=34
21:21:57.335 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:22:02.338 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:22:02.338 - MÉTODO EXECUTADO - TESTE DE LOG
21:22:02.338 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:22:02.339 - Análise da amostra: Wins=0/1, Losses=1/0
21:22:02.339 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:22:02.339 - Verificando se pode disparar compra real...
21:22:02.340 - Estado das tabelas: Compras=20, Simulação=35
21:22:02.340 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:22:02.340 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:22:02.340 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:22:02.341 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:22:02.341 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:22:02.343 - Compra real disparada com sucesso!
21:22:07.327 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:22:07.327 - MÉTODO EXECUTADO - TESTE DE LOG
21:22:07.328 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:22:07.328 - Análise da amostra: Wins=0/1, Losses=1/0
21:22:07.328 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:22:07.329 - Verificando se pode disparar compra real...
21:22:07.329 - Estado das tabelas: Compras=20, Simulação=36
21:22:07.329 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:22:07.330 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:22:07.330 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:22:07.330 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:22:07.330 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:22:07.334 - Compra real disparada com sucesso!
21:22:12.324 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:22:12.326 - MÉTODO EXECUTADO - TESTE DE LOG
21:22:12.326 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:22:12.326 - Análise da amostra: Wins=0/1, Losses=1/0
21:22:12.327 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:22:12.327 - Verificando se pode disparar compra real...
21:22:12.327 - Estado das tabelas: Compras=20, Simulação=37
21:22:12.327 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:22:12.328 - REAL PURCHASE MARTINGALE: Aplicado - Level=3/8, Stake=3,24
21:22:12.328 - REAL PURCHASE STAKE: Calculada - Level=3, Stake=3,24
21:22:12.328 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=3,24
21:22:12.328 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:22:12.330 - Compra real disparada com sucesso!
21:22:17.329 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:22:17.331 - MÉTODO EXECUTADO - TESTE DE LOG
21:22:17.332 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:22:17.332 - Análise da amostra: Wins=1/1, Losses=0/0
21:22:17.332 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:22:17.332 - Verificando se pode disparar compra real...
21:22:17.332 - Estado das tabelas: Compras=20, Simulação=38
21:22:17.333 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:22:17.333 - REAL PURCHASE MARTINGALE: Aplicado - Level=4/8, Stake=6,81
21:22:17.333 - REAL PURCHASE STAKE: Calculada - Level=4, Stake=6,81
21:22:17.334 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=6,81
21:22:17.334 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:22:17.337 - Compra real disparada com sucesso!
21:22:22.332 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:22:22.333 - MÉTODO EXECUTADO - TESTE DE LOG
21:22:22.333 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:22:22.333 - Análise da amostra: Wins=1/1, Losses=0/0
21:22:22.333 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:22:22.334 - Verificando se pode disparar compra real...
21:22:22.334 - Estado das tabelas: Compras=20, Simulação=39
21:22:22.334 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:22:22.335 - REAL PURCHASE MARTINGALE: Aplicado - Level=5/8, Stake=14,29
21:22:22.335 - REAL PURCHASE STAKE: Calculada - Level=5, Stake=14,29
21:22:22.335 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=14,29
21:22:22.335 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:22:22.338 - Compra real disparada com sucesso!
21:32:49.986 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:32:49.986 - MÉTODO EXECUTADO - TESTE DE LOG
21:32:49.986 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:32:49.988 - Análise da amostra: Wins=0/1, Losses=1/0
21:32:49.988 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:32:49.989 - Verificando se pode disparar compra real...
21:32:49.989 - Estado das tabelas: Compras=0, Simulação=1
21:32:49.990 - REAL PURCHASE STAKE: Primeira compra - Stake=0,35
21:32:49.990 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:32:49.990 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:32:50.180 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:32:50.181 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:32:50.182 - EXECUTE REAL PURCHASE: Started
21:32:50.183 - Compra real disparada com sucesso!
21:32:55.984 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:32:55.984 - MÉTODO EXECUTADO - TESTE DE LOG
21:32:55.985 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:32:55.986 - Análise da amostra: Wins=1/1, Losses=0/0
21:32:55.986 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:32:55.987 - Verificando se pode disparar compra real...
21:32:55.988 - Estado das tabelas: Compras=1, Simulação=2
21:32:55.989 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:00.977 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:00.978 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:00.978 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:00.978 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:00.979 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:00.979 - Verificando se pode disparar compra real...
21:33:00.979 - Estado das tabelas: Compras=1, Simulação=3
21:33:00.980 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:33:00.981 - REAL PURCHASE STATS: Novo Max Level = 1
21:33:00.981 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:33:00.981 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:33:00.982 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:33:00.982 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:33:00.982 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:33:00.986 - Compra real disparada com sucesso!
21:33:05.989 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:05.990 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:05.990 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:05.991 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:05.991 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:05.991 - Verificando se pode disparar compra real...
21:33:05.991 - Estado das tabelas: Compras=2, Simulação=4
21:33:05.992 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:07.999 - EXECUTE REAL PURCHASE: Started
21:33:10.986 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:10.986 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:10.986 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:10.987 - Análise da amostra: Wins=1/1, Losses=0/0
21:33:10.987 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:33:10.987 - Verificando se pode disparar compra real...
21:33:10.987 - Estado das tabelas: Compras=2, Simulação=5
21:33:10.988 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:33:10.988 - REAL PURCHASE STATS: Novo Max Level = 2
21:33:10.988 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:33:10.989 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:33:10.989 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:33:10.989 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:33:10.989 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:33:10.991 - Compra real disparada com sucesso!
21:33:15.990 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:15.990 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:15.991 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:15.991 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:15.991 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:15.991 - Verificando se pode disparar compra real...
21:33:15.991 - Estado das tabelas: Compras=3, Simulação=6
21:33:15.992 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-1,54
21:33:15.992 - REAL PURCHASE STATS: Novo Max Level = 3
21:33:15.992 - REAL PURCHASE MARTINGALE: Aplicado - Level=3/8, Stake=3,24
21:33:15.993 - REAL PURCHASE SEQUENCE: Length=4, TotalStake=5,87
21:33:15.993 - REAL PURCHASE STAKE: Calculada - Level=3, Stake=3,24
21:33:15.993 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=3,24
21:33:15.993 - MAIN TRIGGER: CanExecute=False, IsWaiting=True, PendingId=288243680188
21:33:15.994 - MAIN TRIGGER: QUEUED devido a transação pendente
21:33:15.994 - Compra real disparada com sucesso!
21:33:15.998 - EXECUTE REAL PURCHASE: Started
21:33:20.982 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:20.983 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:20.984 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:20.985 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:20.987 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:20.988 - Verificando se pode disparar compra real...
21:33:20.990 - Estado das tabelas: Compras=4, Simulação=7
21:33:20.991 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:23.029 - EXECUTE REAL PURCHASE: Started
21:33:25.986 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:25.987 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:25.988 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:25.988 - Análise da amostra: Wins=1/1, Losses=0/0
21:33:25.989 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:33:25.990 - Verificando se pode disparar compra real...
21:33:25.990 - Estado das tabelas: Compras=5, Simulação=8
21:33:25.991 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:30.977 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:30.977 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:30.978 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:30.979 - Análise da amostra: Wins=1/1, Losses=0/0
21:33:30.979 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:33:30.980 - Verificando se pode disparar compra real...
21:33:30.981 - Estado das tabelas: Compras=5, Simulação=9
21:33:30.983 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:33:30.986 - REAL PURCHASE STATS: Nova maior sequência - TotalStake=5,87
21:33:30.988 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:33:30.989 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:33:30.990 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:33:30.992 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:33:31.004 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:33:31.008 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:33:31.008 - EXECUTE REAL PURCHASE: Started
21:33:31.009 - Compra real disparada com sucesso!
21:33:35.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:35.992 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:35.992 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:35.993 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:35.993 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:35.993 - Verificando se pode disparar compra real...
21:33:35.994 - Estado das tabelas: Compras=6, Simulação=10
21:33:35.994 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:40.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:40.992 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:40.992 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:40.993 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:40.993 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:40.994 - Verificando se pode disparar compra real...
21:33:40.995 - Estado das tabelas: Compras=6, Simulação=11
21:33:40.996 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:33:40.997 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:33:40.998 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:33:40.999 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:33:41.000 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:33:41.000 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:33:41.009 - Compra real disparada com sucesso!
21:33:45.985 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:45.986 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:45.987 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:45.988 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:45.988 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:45.989 - Verificando se pode disparar compra real...
21:33:45.989 - Estado das tabelas: Compras=7, Simulação=12
21:33:45.990 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:33:48.054 - EXECUTE REAL PURCHASE: Started
21:33:50.984 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:50.984 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:50.986 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:50.987 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:50.988 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:50.989 - Verificando se pode disparar compra real...
21:33:50.990 - Estado das tabelas: Compras=7, Simulação=13
21:33:50.992 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:33:50.992 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:33:50.994 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:33:50.995 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:33:50.996 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:33:50.998 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:33:51.013 - Compra real disparada com sucesso!
21:33:55.987 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:33:55.988 - MÉTODO EXECUTADO - TESTE DE LOG
21:33:55.988 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:33:55.989 - Análise da amostra: Wins=0/1, Losses=1/0
21:33:55.990 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:33:55.991 - Verificando se pode disparar compra real...
21:33:55.992 - Estado das tabelas: Compras=8, Simulação=14
21:33:55.993 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-1,54
21:33:55.994 - REAL PURCHASE MARTINGALE: Aplicado - Level=3/8, Stake=3,24
21:33:55.995 - REAL PURCHASE SEQUENCE: Length=4, TotalStake=5,87
21:33:55.995 - REAL PURCHASE STAKE: Calculada - Level=3, Stake=3,24
21:33:55.997 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=3,24
21:33:55.997 - MAIN TRIGGER: CanExecute=False, IsWaiting=True, PendingId=288243716848
21:33:56.001 - MAIN TRIGGER: QUEUED devido a transação pendente
21:33:56.002 - Compra real disparada com sucesso!
21:33:56.015 - EXECUTE REAL PURCHASE: Started
21:34:00.985 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:00.986 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:00.987 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:00.988 - Análise da amostra: Wins=0/1, Losses=1/0
21:34:00.990 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:34:00.991 - Verificando se pode disparar compra real...
21:34:00.992 - Estado das tabelas: Compras=9, Simulação=15
21:34:00.993 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:34:03.045 - EXECUTE REAL PURCHASE: Started
21:34:05.981 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:05.982 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:05.983 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:05.983 - Análise da amostra: Wins=1/1, Losses=0/0
21:34:05.984 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:34:05.985 - Verificando se pode disparar compra real...
21:34:05.985 - Estado das tabelas: Compras=10, Simulação=16
21:34:05.986 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:34:10.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:10.992 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:10.992 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:10.993 - Análise da amostra: Wins=1/1, Losses=0/0
21:34:10.993 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:34:10.994 - Verificando se pode disparar compra real...
21:34:10.994 - Estado das tabelas: Compras=10, Simulação=17
21:34:10.995 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:34:10.995 - REAL PURCHASE STATS: Sequência menor - Atual=5,87, Mantendo=5,87
21:34:10.996 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:34:10.996 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:34:10.997 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:34:10.997 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:34:11.000 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:34:11.001 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:34:11.002 - EXECUTE REAL PURCHASE: Started
21:34:11.002 - Compra real disparada com sucesso!
21:34:15.982 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:15.983 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:15.983 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:15.984 - Análise da amostra: Wins=0/1, Losses=1/0
21:34:15.984 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:34:15.985 - Verificando se pode disparar compra real...
21:34:15.985 - Estado das tabelas: Compras=11, Simulação=18
21:34:15.986 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:34:20.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:20.992 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:20.993 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:20.994 - Análise da amostra: Wins=0/1, Losses=1/0
21:34:20.995 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:34:20.996 - Verificando se pode disparar compra real...
21:34:20.997 - Estado das tabelas: Compras=11, Simulação=19
21:34:20.998 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:34:20.999 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:34:20.999 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:34:21.000 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:34:21.001 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:34:21.002 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:34:21.015 - Compra real disparada com sucesso!
21:34:25.990 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:34:25.991 - MÉTODO EXECUTADO - TESTE DE LOG
21:34:25.991 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:34:25.992 - Análise da amostra: Wins=0/1, Losses=1/0
21:34:25.992 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:34:25.993 - Verificando se pode disparar compra real...
21:34:25.993 - Estado das tabelas: Compras=12, Simulação=20
21:34:25.993 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:35:17.500 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:17.500 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:17.500 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:17.501 - Análise da amostra: Wins=1/1, Losses=0/0
21:35:17.501 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:35:17.502 - Verificando se pode disparar compra real...
21:35:17.502 - Estado das tabelas: Compras=0, Simulação=1
21:35:17.503 - REAL PURCHASE STAKE: Primeira compra - Stake=0,35
21:35:17.504 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:35:17.504 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:17.508 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:35:17.508 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:35:17.509 - EXECUTE REAL PURCHASE: Started
21:35:17.509 - Compra real disparada com sucesso!
21:35:23.509 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:23.510 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:23.510 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:23.511 - Análise da amostra: Wins=0/1, Losses=1/0
21:35:23.511 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:35:23.511 - Verificando se pode disparar compra real...
21:35:23.512 - Estado das tabelas: Compras=1, Simulação=2
21:35:23.512 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:35:28.503 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:28.504 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:28.504 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:28.504 - Análise da amostra: Wins=1/1, Losses=0/0
21:35:28.504 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:35:28.505 - Verificando se pode disparar compra real...
21:35:28.505 - Estado das tabelas: Compras=1, Simulação=3
21:35:28.505 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:35:28.506 - REAL PURCHASE STATS: Novo Max Level = 1
21:35:28.514 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:35:28.514 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:35:28.515 - REAL PURCHASE STATS: MaxLevel=1, TotalStake=0,00
21:35:28.515 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:35:28.515 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:35:28.516 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:28.520 - Compra real disparada com sucesso!
21:35:33.499 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:33.500 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:33.500 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:33.500 - Análise da amostra: Wins=0/1, Losses=1/0
21:35:33.500 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:35:33.501 - Verificando se pode disparar compra real...
21:35:33.501 - Estado das tabelas: Compras=2, Simulação=4
21:35:33.501 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:35:34.514 - EXECUTE REAL PURCHASE: Started
21:35:38.503 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:38.504 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:38.504 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:38.505 - Análise da amostra: Wins=0/1, Losses=1/0
21:35:38.505 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:35:38.505 - Verificando se pode disparar compra real...
21:35:38.505 - Estado das tabelas: Compras=3, Simulação=5
21:35:38.505 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:35:43.501 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:43.502 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:43.502 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:43.502 - Análise da amostra: Wins=1/1, Losses=0/0
21:35:43.502 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:35:43.503 - Verificando se pode disparar compra real...
21:35:43.503 - Estado das tabelas: Compras=3, Simulação=6
21:35:43.503 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:35:43.504 - REAL PURCHASE STATS: Novo Max Level = 2
21:35:43.504 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:35:43.504 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:35:43.504 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=0,00
21:35:43.504 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:35:43.505 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:35:43.505 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:43.507 - Compra real disparada com sucesso!
21:35:48.511 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:48.511 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:48.512 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:48.512 - Análise da amostra: Wins=0/1, Losses=1/0
21:35:48.512 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:35:48.512 - Verificando se pode disparar compra real...
21:35:48.513 - Estado das tabelas: Compras=3, Simulação=7
21:35:48.513 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:35:48.513 - REAL PURCHASE STATS: Novo Max Level = 3
21:35:48.514 - REAL PURCHASE MARTINGALE: Aplicado - Level=3/8, Stake=3,24
21:35:48.514 - REAL PURCHASE SEQUENCE: Length=4, TotalStake=5,87
21:35:48.514 - REAL PURCHASE STATS: MaxLevel=3, TotalStake=0,00
21:35:48.514 - REAL PURCHASE STAKE: Calculada - Level=3, Stake=3,24
21:35:48.514 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=3,24
21:35:48.515 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:48.517 - Compra real disparada com sucesso!
21:35:53.504 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:53.505 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:53.505 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:53.505 - Análise da amostra: Wins=0/1, Losses=1/0
21:35:53.506 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:35:53.506 - Verificando se pode disparar compra real...
21:35:53.506 - Estado das tabelas: Compras=3, Simulação=8
21:35:53.506 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:35:53.506 - REAL PURCHASE STATS: Novo Max Level = 4
21:35:53.506 - REAL PURCHASE MARTINGALE: Aplicado - Level=4/8, Stake=6,81
21:35:53.506 - REAL PURCHASE SEQUENCE: Length=5, TotalStake=12,68
21:35:53.507 - REAL PURCHASE STATS: MaxLevel=4, TotalStake=0,00
21:35:53.507 - REAL PURCHASE STAKE: Calculada - Level=4, Stake=6,81
21:35:53.507 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=6,81
21:35:53.508 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:53.510 - Compra real disparada com sucesso!
21:35:58.507 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:35:58.508 - MÉTODO EXECUTADO - TESTE DE LOG
21:35:58.508 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:35:58.508 - Análise da amostra: Wins=1/1, Losses=0/0
21:35:58.508 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:35:58.509 - Verificando se pode disparar compra real...
21:35:58.509 - Estado das tabelas: Compras=3, Simulação=9
21:35:58.510 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:35:58.510 - REAL PURCHASE STATS: Novo Max Level = 5
21:35:58.511 - REAL PURCHASE MARTINGALE: Aplicado - Level=5/8, Stake=14,29
21:35:58.511 - REAL PURCHASE SEQUENCE: Length=6, TotalStake=26,97
21:35:58.511 - REAL PURCHASE STATS: MaxLevel=5, TotalStake=0,00
21:35:58.511 - REAL PURCHASE STAKE: Calculada - Level=5, Stake=14,29
21:35:58.512 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=14,29
21:35:58.512 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:35:58.514 - Compra real disparada com sucesso!
21:36:03.509 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:36:03.509 - MÉTODO EXECUTADO - TESTE DE LOG
21:36:03.510 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:36:03.510 - Análise da amostra: Wins=0/1, Losses=1/0
21:36:03.510 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:36:03.511 - Verificando se pode disparar compra real...
21:36:03.511 - Estado das tabelas: Compras=3, Simulação=10
21:36:03.512 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,74
21:36:03.512 - REAL PURCHASE STATS: Novo Max Level = 6
21:36:03.513 - REAL PURCHASE MARTINGALE: Aplicado - Level=6/8, Stake=30,02
21:36:03.513 - REAL PURCHASE SEQUENCE: Length=7, TotalStake=56,99
21:36:03.513 - REAL PURCHASE STATS: MaxLevel=6, TotalStake=0,00
21:36:03.513 - REAL PURCHASE STAKE: Calculada - Level=6, Stake=30,02
21:36:03.514 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=30,02
21:36:03.514 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:36:03.517 - Compra real disparada com sucesso!
21:36:46.000 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:36:46.001 - MÉTODO EXECUTADO - TESTE DE LOG
21:36:46.001 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:36:46.002 - Análise da amostra: Wins=0/1, Losses=1/0
21:36:46.003 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:36:46.003 - Verificando se pode disparar compra real...
21:36:46.003 - Estado das tabelas: Compras=0, Simulação=1
21:36:46.004 - REAL PURCHASE STAKE: Primeira compra - Stake=0,35
21:36:46.004 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:36:46.005 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:36:46.009 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:36:46.010 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:36:46.010 - EXECUTE REAL PURCHASE: Started
21:36:46.011 - Compra real disparada com sucesso!
21:36:50.998 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:36:50.999 - MÉTODO EXECUTADO - TESTE DE LOG
21:36:50.999 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:36:51.000 - Análise da amostra: Wins=0/1, Losses=1/0
21:36:51.000 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:36:51.000 - Verificando se pode disparar compra real...
21:36:51.001 - Estado das tabelas: Compras=1, Simulação=2
21:36:51.001 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:36:55.999 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:36:56.000 - MÉTODO EXECUTADO - TESTE DE LOG
21:36:56.005 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:36:56.005 - Análise da amostra: Wins=1/1, Losses=0/0
21:36:56.006 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:36:56.006 - Verificando se pode disparar compra real...
21:36:56.006 - Estado das tabelas: Compras=1, Simulação=3
21:36:56.010 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:36:56.012 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=0, SequenceLength=1, SequenceStake=0,35
21:36:56.014 - REAL PURCHASE STATS: Sequência única (sem martingale) - Stake=0,35 - NÃO registrada
21:36:56.015 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:36:56.015 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:36:56.017 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:36:56.018 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:36:56.024 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:36:56.024 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:36:56.024 - EXECUTE REAL PURCHASE: Started
21:36:56.025 - Compra real disparada com sucesso!
21:37:00.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:00.991 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:00.991 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:00.991 - Análise da amostra: Wins=1/1, Losses=0/0
21:37:00.992 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:37:00.992 - Verificando se pode disparar compra real...
21:37:00.992 - Estado das tabelas: Compras=2, Simulação=4
21:37:00.992 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:37:05.997 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:05.997 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:05.998 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:05.998 - Análise da amostra: Wins=0/1, Losses=1/0
21:37:05.998 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:37:05.999 - Verificando se pode disparar compra real...
21:37:06.001 - Estado das tabelas: Compras=2, Simulação=5
21:37:06.003 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:37:06.004 - REAL PURCHASE STATS: Novo Max Level = 1
21:37:06.006 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:37:06.006 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:37:06.006 - REAL PURCHASE STATS: MaxLevel=1, TotalStake=0,00
21:37:06.007 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:37:06.007 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:37:06.008 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:37:06.011 - Compra real disparada com sucesso!
21:37:11.000 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:11.001 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:11.001 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:11.001 - Análise da amostra: Wins=0/1, Losses=1/0
21:37:11.002 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:37:11.002 - Verificando se pode disparar compra real...
21:37:11.002 - Estado das tabelas: Compras=3, Simulação=6
21:37:11.003 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:37:13.021 - EXECUTE REAL PURCHASE: Started
21:37:15.991 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:15.991 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:15.991 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:15.992 - Análise da amostra: Wins=1/1, Losses=0/0
21:37:15.992 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:37:15.992 - Verificando se pode disparar compra real...
21:37:15.993 - Estado das tabelas: Compras=4, Simulação=7
21:37:15.993 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:37:20.988 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:20.989 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:20.989 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:20.990 - Análise da amostra: Wins=0/1, Losses=1/0
21:37:20.990 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:37:20.990 - Verificando se pode disparar compra real...
21:37:20.990 - Estado das tabelas: Compras=4, Simulação=8
21:37:20.991 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-1,54
21:37:20.991 - REAL PURCHASE STATS: Novo Max Level = 2
21:37:20.991 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:37:20.992 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:37:20.992 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=0,00
21:37:20.992 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:37:20.992 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:37:20.993 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:37:20.995 - Compra real disparada com sucesso!
21:37:25.990 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:37:25.990 - MÉTODO EXECUTADO - TESTE DE LOG
21:37:25.990 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:37:25.991 - Análise da amostra: Wins=0/1, Losses=1/0
21:37:25.991 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:37:25.992 - Verificando se pode disparar compra real...
21:37:25.992 - Estado das tabelas: Compras=5, Simulação=9
21:37:25.992 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:45:56.485 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:45:56.486 - MÉTODO EXECUTADO - TESTE DE LOG
21:45:56.486 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:45:56.487 - Análise da amostra: Wins=0/1, Losses=1/0
21:45:56.487 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:45:56.488 - Verificando se pode disparar compra real...
21:45:56.488 - Estado das tabelas: Compras=0, Simulação=1
21:45:56.489 - REAL PURCHASE STAKE: Primeira compra - Stake=0,35
21:45:56.489 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:45:56.490 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:45:56.738 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:45:56.739 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:45:56.740 - EXECUTE REAL PURCHASE: Started
21:45:56.740 - Compra real disparada com sucesso!
21:46:01.489 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:01.489 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:01.490 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:01.490 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:01.490 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:01.491 - Verificando se pode disparar compra real...
21:46:01.491 - Estado das tabelas: Compras=1, Simulação=2
21:46:01.491 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:07.483 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:07.483 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:07.484 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:07.484 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:07.484 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:07.485 - Verificando se pode disparar compra real...
21:46:07.485 - Estado das tabelas: Compras=1, Simulação=3
21:46:07.486 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:46:07.487 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=0, SequenceLength=1, SequenceStake=0,35
21:46:07.487 - REAL PURCHASE STATS: Sequência única (sem martingale) - Stake=0,35 - NÃO registrada
21:46:07.487 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:46:07.487 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:46:07.487 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:46:07.488 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:46:07.490 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:46:07.490 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:46:07.490 - EXECUTE REAL PURCHASE: Started
21:46:07.491 - Compra real disparada com sucesso!
21:46:12.487 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:12.488 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:12.488 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:12.489 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:12.490 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:12.490 - Verificando se pode disparar compra real...
21:46:12.490 - Estado das tabelas: Compras=2, Simulação=4
21:46:12.490 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:17.491 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:17.492 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:17.492 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:17.493 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:17.493 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:17.493 - Verificando se pode disparar compra real...
21:46:17.494 - Estado das tabelas: Compras=2, Simulação=5
21:46:17.494 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:46:17.494 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=0, SequenceLength=1, SequenceStake=0,35
21:46:17.494 - REAL PURCHASE STATS: Sequência única (sem martingale) - Stake=0,35 - NÃO registrada
21:46:17.495 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:46:17.495 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:46:17.495 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:46:17.495 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:46:17.496 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:46:17.497 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:46:17.497 - EXECUTE REAL PURCHASE: Started
21:46:17.497 - Compra real disparada com sucesso!
21:46:22.479 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:22.479 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:22.480 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:22.481 - Análise da amostra: Wins=0/1, Losses=1/0
21:46:22.481 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:46:22.481 - Verificando se pode disparar compra real...
21:46:22.482 - Estado das tabelas: Compras=3, Simulação=6
21:46:22.482 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:27.483 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:27.484 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:27.484 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:27.484 - Análise da amostra: Wins=0/1, Losses=1/0
21:46:27.484 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:46:27.485 - Verificando se pode disparar compra real...
21:46:27.485 - Estado das tabelas: Compras=3, Simulação=7
21:46:27.486 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:46:27.486 - REAL PURCHASE STATS: Novo Max Level = 1
21:46:27.486 - REAL PURCHASE STATS: Nova maior sequência em andamento - TotalStake=1,09
21:46:27.487 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:46:27.487 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:46:27.487 - REAL PURCHASE STATS: MaxLevel=1, TotalStake=1,09
21:46:27.487 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:46:27.488 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:46:27.488 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:46:27.490 - Compra real disparada com sucesso!
21:46:32.478 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:32.479 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:32.479 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:32.479 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:32.480 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:32.480 - Verificando se pode disparar compra real...
21:46:32.481 - Estado das tabelas: Compras=4, Simulação=8
21:46:32.481 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:34.506 - EXECUTE REAL PURCHASE: Started
21:46:37.481 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:37.482 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:37.482 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:37.482 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:37.483 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:37.483 - Verificando se pode disparar compra real...
21:46:37.483 - Estado das tabelas: Compras=5, Simulação=9
21:46:37.484 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:42.480 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:42.481 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:42.481 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:42.482 - Análise da amostra: Wins=0/1, Losses=1/0
21:46:42.482 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:46:42.483 - Verificando se pode disparar compra real...
21:46:42.483 - Estado das tabelas: Compras=5, Simulação=10
21:46:42.484 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:46:42.484 - REAL PURCHASE STATS: Novo Max Level = 2
21:46:42.485 - REAL PURCHASE STATS: Nova maior sequência em andamento - TotalStake=2,63
21:46:42.485 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:46:42.486 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:46:42.486 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=2,63
21:46:42.487 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:46:42.487 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:46:42.488 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:46:42.493 - Compra real disparada com sucesso!
21:46:47.492 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:47.493 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:47.493 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:47.494 - Análise da amostra: Wins=0/1, Losses=1/0
21:46:47.494 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:46:47.495 - Verificando se pode disparar compra real...
21:46:47.495 - Estado das tabelas: Compras=6, Simulação=11
21:46:47.496 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:46:51.498 - EXECUTE REAL PURCHASE: Started
21:46:52.486 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:52.486 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:52.487 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:52.488 - Análise da amostra: Wins=1/1, Losses=0/0
21:46:52.488 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:46:52.489 - Verificando se pode disparar compra real...
21:46:52.489 - Estado das tabelas: Compras=6, Simulação=12
21:46:52.490 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,70
21:46:52.490 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=2, SequenceLength=3, SequenceStake=2,63
21:46:52.490 - REAL PURCHASE RESET: Finalizando sequência - Length=3, TotalStake registrado=2,63
21:46:52.491 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:46:52.491 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:46:52.492 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:46:52.492 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:46:52.495 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:46:52.496 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:46:52.496 - EXECUTE REAL PURCHASE: Started
21:46:52.497 - Compra real disparada com sucesso!
21:46:57.491 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:46:57.491 - MÉTODO EXECUTADO - TESTE DE LOG
21:46:57.492 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:46:57.492 - Análise da amostra: Wins=0/1, Losses=1/0
21:46:57.492 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:46:57.493 - Verificando se pode disparar compra real...
21:46:57.493 - Estado das tabelas: Compras=8, Simulação=13
21:46:57.494 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:02.487 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:02.488 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:02.488 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:02.488 - Análise da amostra: Wins=1/1, Losses=0/0
21:47:02.489 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:47:02.489 - Verificando se pode disparar compra real...
21:47:02.489 - Estado das tabelas: Compras=8, Simulação=14
21:47:02.489 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=0,31
21:47:02.489 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=0, SequenceLength=1, SequenceStake=0,35
21:47:02.490 - REAL PURCHASE STATS: Sequência única (sem martingale) - Stake=0,35 - NÃO registrada
21:47:02.490 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:47:02.490 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:47:02.490 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:47:02.490 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:47:02.493 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:47:02.494 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:47:02.494 - EXECUTE REAL PURCHASE: Started
21:47:02.494 - Compra real disparada com sucesso!
21:47:07.490 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:07.491 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:07.491 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:07.491 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:07.492 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:07.492 - Verificando se pode disparar compra real...
21:47:07.493 - Estado das tabelas: Compras=9, Simulação=15
21:47:07.493 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:12.487 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:12.487 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:12.488 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:12.488 - Análise da amostra: Wins=1/1, Losses=0/0
21:47:12.488 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:47:12.488 - Verificando se pode disparar compra real...
21:47:12.488 - Estado das tabelas: Compras=9, Simulação=16
21:47:12.489 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:47:12.489 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:47:12.489 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:47:12.489 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=2,63
21:47:12.489 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:47:12.489 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:47:12.490 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:47:12.492 - Compra real disparada com sucesso!
21:47:17.477 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:17.477 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:17.478 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:17.478 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:17.478 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:17.479 - Verificando se pode disparar compra real...
21:47:17.479 - Estado das tabelas: Compras=10, Simulação=17
21:47:17.479 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:19.501 - EXECUTE REAL PURCHASE: Started
21:47:22.487 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:22.487 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:22.488 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:22.488 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:22.488 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:22.488 - Verificando se pode disparar compra real...
21:47:22.488 - Estado das tabelas: Compras=11, Simulação=18
21:47:22.489 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:27.480 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:27.480 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:27.480 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:27.481 - Análise da amostra: Wins=1/1, Losses=0/0
21:47:27.481 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:47:27.482 - Verificando se pode disparar compra real...
21:47:27.482 - Estado das tabelas: Compras=11, Simulação=19
21:47:27.483 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-1,54
21:47:27.483 - REAL PURCHASE MARTINGALE: Aplicado - Level=2/8, Stake=1,54
21:47:27.483 - REAL PURCHASE SEQUENCE: Length=3, TotalStake=2,63
21:47:27.484 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=2,63
21:47:27.484 - REAL PURCHASE STAKE: Calculada - Level=2, Stake=1,54
21:47:27.485 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=1,54
21:47:27.485 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:47:27.489 - Compra real disparada com sucesso!
21:47:32.480 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:32.481 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:32.481 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:32.482 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:32.482 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:32.482 - Verificando se pode disparar compra real...
21:47:32.483 - Estado das tabelas: Compras=12, Simulação=20
21:47:32.483 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:36.505 - EXECUTE REAL PURCHASE: Started
21:47:37.477 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:37.478 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:37.478 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:37.479 - Análise da amostra: Wins=1/1, Losses=0/0
21:47:37.479 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:47:37.480 - Verificando se pode disparar compra real...
21:47:37.480 - Estado das tabelas: Compras=12, Simulação=21
21:47:37.480 - REAL PURCHASE STAKE: Última transação foi VITÓRIA - P/L=3,09
21:47:37.481 - REAL PURCHASE RESET: Iniciando reset - CurrentLevel=2, SequenceLength=3, SequenceStake=2,63
21:47:37.481 - REAL PURCHASE RESET: Finalizando sequência - Length=3, TotalStake registrado=2,63
21:47:37.481 - REAL PURCHASE MARTINGALE: Reset - Level=0, Stake=0,35
21:47:37.481 - REAL PURCHASE STAKE: Calculada - Level=0, Stake=0,35
21:47:37.482 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,35
21:47:37.482 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:47:37.484 - MAIN TRIGGER: EXECUTING REAL PURCHASE
21:47:37.485 - STAKE UPDATE: 0.35 → 0.35 (Level=0)
21:47:37.486 - EXECUTE REAL PURCHASE: Started
21:47:37.487 - Compra real disparada com sucesso!
21:47:42.477 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:42.477 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:42.478 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:42.478 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:42.478 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:42.478 - Verificando se pode disparar compra real...
21:47:42.478 - Estado das tabelas: Compras=13, Simulação=22
21:47:42.479 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:47.488 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:47.488 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:47.489 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:47.489 - Análise da amostra: Wins=1/1, Losses=0/0
21:47:47.489 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 vitórias (meta: 1)
21:47:47.489 - Verificando se pode disparar compra real...
21:47:47.489 - Estado das tabelas: Compras=13, Simulação=23
21:47:47.489 - REAL PURCHASE STAKE: Última transação foi DERROTA - P/L=-0,35
21:47:47.490 - REAL PURCHASE MARTINGALE: Aplicado - Level=1/8, Stake=0,74
21:47:47.490 - REAL PURCHASE SEQUENCE: Length=2, TotalStake=1,09
21:47:47.490 - REAL PURCHASE STATS: MaxLevel=2, TotalStake=2,63
21:47:47.490 - REAL PURCHASE STAKE: Calculada - Level=1, Stake=0,74
21:47:47.490 - TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake=0,74
21:47:47.490 - MAIN TRIGGER: CanExecute=True, IsWaiting=False, PendingId=NULL
21:47:47.492 - Compra real disparada com sucesso!
21:47:52.477 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:52.477 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:52.478 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:52.478 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:52.478 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:52.478 - Verificando se pode disparar compra real...
21:47:52.478 - Estado das tabelas: Compras=14, Simulação=24
21:47:52.479 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
21:47:54.496 - EXECUTE REAL PURCHASE: Started
21:47:57.490 - INICIANDO CheckSampleConditionsAndTriggerRealPurchase()
21:47:57.491 - MÉTODO EXECUTADO - TESTE DE LOG
21:47:57.492 - Verificando condições da amostra: Amostra=1, TargetWins=1, TargetLosses=0
21:47:57.494 - Análise da amostra: Wins=0/1, Losses=1/0
21:47:57.495 - CONDIÇÕES DA AMOSTRA ATINGIDAS: Atingiu 1 derrotas (meta: 0)
21:47:57.496 - Verificando se pode disparar compra real...
21:47:57.496 - Estado das tabelas: Compras=15, Simulação=25
21:47:57.496 - Já existe operação ativa na tabela Compras. Não disparando nova compra.
