using System.Collections.ObjectModel;
using System.Windows.Input;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Models.DTOs.ApiResponses;
using Microsoft.Extensions.Logging;
using Excalibur.Infrastructure.Commands;

namespace Excalibur.ViewModels;

public class ActiveSymbolsViewModel : BaseViewModel
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<ActiveSymbolsViewModel> _logger;

    public ObservableCollection<ActiveSymbol> Symbols { get; } = new();
    public ICommand ToggleSymbolCommand { get; }
    public ICommand SelectContractCommand { get; }
    
    public event EventHandler<(string Symbol, ContractType Contract)>? ContractSelected;

    public ActiveSymbolsViewModel(IDerivApiService derivApiService, ILogger<ActiveSymbolsViewModel> logger)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        _derivApiService.ActiveSymbolsReceived += OnActiveSymbolsReceived;
        _derivApiService.ContractsForReceived += OnContractsForReceived;
        
        ToggleSymbolCommand = new RelayCommand<ActiveSymbol>(ToggleSymbol);
        SelectContractCommand = new RelayCommand<object>(SelectContract);
    }

    private void OnActiveSymbolsReceived(object? sender, List<ActiveSymbol> symbols)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            System.Diagnostics.Debug.WriteLine($"[DEBUG] Received {symbols.Count} symbols");
            _logger.LogInformation("Clearing existing symbols and adding {Count} new symbols", symbols.Count);
            
            Symbols.Clear();
            foreach (var s in symbols)
            {
                Symbols.Add(s);
            }
            
            _logger.LogInformation("Symbols collection populated with {Count} items", Symbols.Count);
            
            // Notificar mudança na propriedade para atualizar UI
            OnPropertyChanged(nameof(Symbols));
        });
    }

    private void OnContractsForReceived(object? sender, (string Symbol, List<ContractType> Contracts) data)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            var symbol = Symbols.FirstOrDefault(s => s.Symbol == data.Symbol);
            if (symbol != null)
            {
                symbol.IsLoadingContracts = false;
                symbol.AvailableContracts.Clear();
                foreach (var contract in data.Contracts)
                {
                    symbol.AvailableContracts.Add(contract);
                }
                _logger.LogInformation("Contratos carregados para {Symbol}: {Count} contratos", data.Symbol, data.Contracts.Count);
            }
        });
    }

    private async void ToggleSymbol(ActiveSymbol? symbol)
    {
        if (symbol == null) return;

        symbol.IsExpanded = !symbol.IsExpanded;
        
        if (symbol.IsExpanded && symbol.AvailableContracts.Count == 0)
        {
            symbol.IsLoadingContracts = true;
            _logger.LogInformation("Carregando contratos para {Symbol}", symbol.Symbol);
            await _derivApiService.RequestContractsForAsync(symbol.Symbol);
        }
    }

    private void SelectContract(object? parameter)
    {
        if (parameter is not ContractType contract) return;
        
        // Encontrar o símbolo que contém este contrato
        var symbol = Symbols.FirstOrDefault(s => s.AvailableContracts.Contains(contract));
        if (symbol != null)
        {
            _logger.LogInformation("Contrato selecionado: {Contract} para {Symbol}", contract.ContractDisplay, symbol.Symbol);
            ContractSelected?.Invoke(this, (symbol.Symbol, contract));
        }
    }
} 