using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Timers;
using System.Collections.Generic;
using Excalibur.Models;

namespace Excalibur.ViewModels;

public class PurchaseViewModel : BaseViewModel, IDisposable
{
    public event EventHandler<ContractTransaction>? ContractExpired;
    private ObservableCollection<ContractTransaction> _purchaseTransactions;
    private int _nextTransactionId = 1;
    private System.Timers.Timer _expirationTimer;
    private decimal _currentSpot;
    private readonly object _lock = new object(); // Objeto de sincronização
    private const int MAX_TRANSACTIONS = 50; // Limite para prevenir acúmulo excessivo
    
    // Controle de martingale para compras
    private int _currentMartingaleLevel = 0;
    private int _maxMartingaleLevelReached = 0;
    private decimal _totalStakeUsedInMaxLevel = 0;
    private decimal _baseStake = 0.35m; // Stake inicial padrão
    
    // Referência para MoneyManagement
    private MoneyManagementViewModel? _moneyManagement;
    private ProposalViewModel? _proposalViewModel;

    public PurchaseViewModel()
    {
        _purchaseTransactions = new ObservableCollection<ContractTransaction>();
        
        // Timer para verificar expiração de contratos a cada segundo
        _expirationTimer = new System.Timers.Timer(1000);
        _expirationTimer.Elapsed += CheckContractExpirations;
        _expirationTimer.AutoReset = true;
        _expirationTimer.Start();
    }

    public ObservableCollection<ContractTransaction> PurchaseTransactions
    {
        get => _purchaseTransactions;
        set
        {
            _purchaseTransactions = value;
            OnPropertyChanged();
        }
    }

    // Propriedades calculadas para estatísticas
    public int TotalPurchaseEntries => _purchaseTransactions?.Count ?? 0;

    // Propriedade para calcular o total de stakes usadas em todas as transações
    public decimal TotalStakeUsed => _purchaseTransactions?.Sum(t => t.Stake) ?? 0;

    // Propriedades para estatísticas de martingale
    public int MaxMartingaleLevelReached
    {
        get => _maxMartingaleLevelReached;
        private set
        {
            _maxMartingaleLevelReached = value;
            OnPropertyChanged();
        }
    }

    public decimal TotalStakeUsedInMaxLevel
    {
        get => _totalStakeUsedInMaxLevel;
        private set
        {
            _totalStakeUsedInMaxLevel = value;
            OnPropertyChanged();
        }
    }

    // Controle da sequência atual de martingale para compras
    private decimal _currentPurchaseSequenceStakeSum = 0;
    private int _currentPurchaseSequenceLength = 0;
    
    public int CurrentMartingaleLevel
    {
        get => _currentMartingaleLevel;
        private set
        {
            _currentMartingaleLevel = value;
            OnPropertyChanged();
        }
    }
    
    public decimal TotalProfitLoss 
    { 
        get 
        { 
            var total = _purchaseTransactions?.Sum(t => t.TotalProfitLoss) ?? 0;
            System.Diagnostics.Debug.WriteLine($"[PURCHASE] Calculando TotalProfitLoss: {total:F2}");
            
            // Debug: listar todas as transações e seus valores
            if (_purchaseTransactions != null)
            {
                foreach (var t in _purchaseTransactions)
                {
                    System.Diagnostics.Debug.WriteLine($"[PURCHASE] Transação ID={t.Id}, RefId={t.RefId}, TotalProfitLoss={t.TotalProfitLoss:F2}");
                }
            }
            
            return total;
        }
    }

    public void AddTransaction(ContractTransaction transaction)
    {
        lock (_lock)
        {
            transaction.Id = _nextTransactionId++;

            // SEMPRE acumular stake na sequência atual (incluindo a primeira que pode virar martingale)
            _currentPurchaseSequenceStakeSum += transaction.Stake;
            _currentPurchaseSequenceLength++;

            if (_currentMartingaleLevel > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 ACUMULANDO STAKE MARTINGALE - Stake: {transaction.Stake:F2}, Nível: {_currentMartingaleLevel}, Soma atual: {_currentPurchaseSequenceStakeSum:F2}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 ACUMULANDO STAKE BASE - Stake: {transaction.Stake:F2}, Nível: {_currentMartingaleLevel}, Soma atual: {_currentPurchaseSequenceStakeSum:F2} (pode virar martingale)");
            }

            _purchaseTransactions.Add(transaction);

            // Limpar transações antigas se exceder o limite
            bool removedAny = false;
            while (_purchaseTransactions.Count > MAX_TRANSACTIONS)
            {
                var oldestTransaction = _purchaseTransactions.FirstOrDefault(t => !t.IsActive);
                if (oldestTransaction != null)
                {
                    _purchaseTransactions.Remove(oldestTransaction);
                    removedAny = true;
                    System.Diagnostics.Debug.WriteLine($"Removed old transaction {oldestTransaction.Id} to prevent memory leak");
                }
                else
                {
                    break; // Não remover transações ativas
                }
            }

            // Se removeu alguma transação, notificar mudança no total
            if (removedAny)
            {
                OnPropertyChanged(nameof(TotalProfitLoss));
            }
        }
        
        // Notificar mudanças nas propriedades calculadas
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
        OnPropertyChanged(nameof(TotalStakeUsed));
    }

    public void UpdateTransaction(string refId, decimal endSpot, decimal totalProfitLoss, bool isClosed = false)
    {
        var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
        if (transaction != null)
        {
            transaction.EndSpot = endSpot;
            transaction.TotalProfitLoss = totalProfitLoss;
            
            if (isClosed)
            {
                transaction.SellTime = DateTime.Now;
                transaction.IsActive = false;
            }
            
            // Notificar mudança no lucro/prejuízo total
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }

    private void RegisterPurchaseMartingaleSequence()
    {
        // Só registrar se houve uma sequência de martingale REAL (chegou a nível > 0)
        // E só se a sequência teve pelo menos 2 transações (uma perdeu, outra ganhou)
        if (_currentPurchaseSequenceLength >= 2 && _currentPurchaseSequenceStakeSum > 0 && _currentMartingaleLevel > 0)
        {
            // Verificar se esta sequência é maior que a anterior
            if (_currentPurchaseSequenceStakeSum > _totalStakeUsedInMaxLevel)
            {
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 NOVA MAIOR SEQUÊNCIA MARTINGALE - Anterior: {_totalStakeUsedInMaxLevel:F2}, Nova: {_currentPurchaseSequenceStakeSum:F2}");
                _totalStakeUsedInMaxLevel = _currentPurchaseSequenceStakeSum;
                TotalStakeUsedInMaxLevel = _totalStakeUsedInMaxLevel;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 Sequência martingale menor - Atual: {_currentPurchaseSequenceStakeSum:F2}, Mantendo: {_totalStakeUsedInMaxLevel:F2}");
            }

            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Sequência martingale registrada - Comprimento: {_currentPurchaseSequenceLength}, Soma: {_currentPurchaseSequenceStakeSum:F2}, Total registrado: {_totalStakeUsedInMaxLevel:F2}");
        }
        else if (_currentPurchaseSequenceLength == 1)
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 Sequência única (sem martingale) - Stake: {_currentPurchaseSequenceStakeSum:F2} - NÃO registrada no Total Stake");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] 📊 Sequência inválida para registro - Comprimento: {_currentPurchaseSequenceLength}, Nível: {_currentMartingaleLevel}");
        }

        // Resetar contadores da sequência atual
        _currentPurchaseSequenceStakeSum = 0;
        _currentPurchaseSequenceLength = 0;
    }
    
    public void UpdateContractId(string oldProposalId, string newContractId)
    {
        var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == oldProposalId);
        if (transaction != null)
        {
            transaction.RefId = newContractId;
        }
    }

    public void UpdateCurrentSpot(decimal currentSpot)
    {
        _currentSpot = currentSpot;
        System.Diagnostics.Debug.WriteLine($"UPDATE_SPOT: Novo spot={currentSpot}, Contratos ativos={_purchaseTransactions.Count(t => t.IsActive)}");
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            // Atualizar apenas contratos ativos
            foreach (var transaction in _purchaseTransactions.Where(t => t.IsActive))
            {
                // Atualizar EndSpot a cada tick para refletir a cotação mais recente
                transaction.EndSpot = currentSpot;

                // Se ainda não temos StartSpot válido, capturar agora (primeiro tick)
                if (transaction.StartSpot == 0)
                {
                    transaction.StartSpot = currentSpot;
                }
                
                // Durante a execução do contrato, mostrar P/L em tempo real baseado na lógica binária (tudo ou nada)
                if (!transaction.ProfitFromApi && transaction.IsActive)
                {
                    // Verificar se está ganhando usando IsCurrentlyWinning
                    bool isWinning = transaction.IsCurrentlyWinning(currentSpot);
                    
                    System.Diagnostics.Debug.WriteLine($"[PURCHASE-RT] ID={transaction.Id}, Type={transaction.Type}, " +
                        $"StartSpot={transaction.StartSpot:F3}, CurrentSpot={currentSpot:F3}, " +
                        $"Diff={(currentSpot - transaction.StartSpot):F3}, IsWinning={isWinning}");
                    
                    decimal maxProfit = transaction.Payout - transaction.Stake;
                    decimal maxLoss = -transaction.Stake;

                    // Calcular variação gradual baseada na distância do strike/barrier
                    decimal gradualPL = CalculateGradualProfitLoss(transaction, currentSpot, maxProfit, maxLoss);
                    transaction.TotalProfitLoss = gradualPL;

                    System.Diagnostics.Debug.WriteLine($"[PURCHASE-RT] {(isWinning ? "✅" : "❌")} P/L Gradual: {gradualPL:F2} (Max: {maxProfit:F2}, Min: {maxLoss:F2})");
                }
            }
        }
        
        // Sempre notificar mudança no lucro/prejuízo total
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    private decimal CalculateGradualProfitLoss(ContractTransaction transaction, decimal currentSpot, decimal maxProfit, decimal maxLoss)
    {
        try
        {
            // Para contratos Higher/Lower, calcular baseado na distância do strike
            if (transaction.Type.Contains("Higher", StringComparison.OrdinalIgnoreCase) ||
                transaction.Type.Contains("Lower", StringComparison.OrdinalIgnoreCase))
            {
                decimal startSpot = transaction.StartSpot;
                decimal spotDifference = Math.Abs(currentSpot - startSpot);

                // Definir uma faixa de variação (por exemplo, 0.1% do preço inicial)
                decimal variationRange = startSpot * 0.001m; // 0.1% do preço inicial

                // Calcular fator de proximidade (0 = no strike, 1 = muito longe)
                decimal proximityFactor = Math.Min(spotDifference / variationRange, 1.0m);

                bool isWinning = transaction.IsCurrentlyWinning(currentSpot);

                if (isWinning)
                {
                    // Se está ganhando, interpolar entre 0 e maxProfit baseado na proximidade
                    return maxProfit * proximityFactor;
                }
                else
                {
                    // Se está perdendo, interpolar entre 0 e maxLoss baseado na proximidade
                    return maxLoss * proximityFactor;
                }
            }

            // Para outros tipos de contrato, usar lógica binária tradicional
            bool isCurrentlyWinning = transaction.IsCurrentlyWinning(currentSpot);
            return isCurrentlyWinning ? maxProfit : maxLoss;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE] Erro ao calcular P/L gradual: {ex.Message}");
            // Em caso de erro, usar lógica binária
            bool isCurrentlyWinning = transaction.IsCurrentlyWinning(currentSpot);
            return isCurrentlyWinning ? maxProfit : maxLoss;
        }
    }

    private void CheckContractExpirations(object? sender, ElapsedEventArgs e)
    {
        var now = DateTime.Now;
        
        List<ContractTransaction> expiredContracts = new List<ContractTransaction>();
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            foreach (var transaction in _purchaseTransactions.Where(t => t.IsActive).ToList())
            {
                // Lógica de expiração: usar duração configurada do contrato
                var timeElapsed = now - transaction.BuyTime;
                var expirationTimeInSeconds = transaction.GetExpirationTimeInSeconds();
                
                // Atualizar EndSpot em tempo real para visualização
                transaction.EndSpot = _currentSpot;
                
                if (timeElapsed.TotalSeconds >= expirationTimeInSeconds)
                {
                    // Contrato expirado - definir End Spot e Sell Time
                    transaction.EndSpot = _currentSpot;
                    
                    // Calcular SellTime exato baseado no BuyTime + duração para precisão
                    var exactSellTime = transaction.BuyTime.AddSeconds(expirationTimeInSeconds);
                    transaction.SellTime = exactSellTime;
                    transaction.IsActive = false;
                    
                    System.Diagnostics.Debug.WriteLine($"[PURCHASE] SellTime calculado: BuyTime={transaction.BuyTime:HH:mm:ss} + {expirationTimeInSeconds}s = {exactSellTime:HH:mm:ss}");

                    // Calcular resultado final baseado no tipo de contrato
                    // Compatível com a lógica da Deriv.com
                    // MAS APENAS SE NÃO TEMOS VALOR DA API
                    if (!transaction.ProfitFromApi)
                    {
                        // Usar cálculo proporcional mais preciso na expiração
                        decimal priceVariation = Math.Abs(_currentSpot - transaction.StartSpot);
                        decimal maxPayout = transaction.Payout - transaction.Stake;
                        
                        if (transaction.IsCurrentlyWinning(_currentSpot))
                        {
                            // Ganhou: usar payout máximo
                            transaction.TotalProfitLoss = maxPayout;
                        }
                        else
                        {
                            // Perdeu: usar perda total (stake)
                            transaction.TotalProfitLoss = -transaction.Stake;
                        }
                    }

                    // Guardar para disparar evento fora do lock
                    expiredContracts.Add(transaction);
                }
            }
        }
        
        // Disparar eventos fora do lock para evitar deadlocks
        foreach (var contract in expiredContracts)
        {
            ContractExpired?.Invoke(this, contract);
        }
        
        // Se houve contratos expirados, notificar mudança no total
        if (expiredContracts.Count > 0)
        {
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }

    public void UpdateProfitFromApi(string refId, decimal profit)
    {
        System.Diagnostics.Debug.WriteLine($"ATUALIZANDO: Tentando atualizar P/L para RefId={refId}, Profit={profit}");
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
            if (transaction != null)
            {
                System.Diagnostics.Debug.WriteLine($"ENCONTRADO: RefId={refId}, P/L anterior={transaction.TotalProfitLoss}, novo P/L={profit}");
                transaction.TotalProfitLoss = profit;
                transaction.ProfitFromApi = true;
                // Forçar atualização da UI
                transaction.OnPropertyChanged(nameof(transaction.TotalProfitLoss));
                transaction.OnPropertyChanged(nameof(transaction.TotalProfitLossString));
                
                // Notificar mudança no total geral
                OnPropertyChanged(nameof(TotalProfitLoss));
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"NÃO ENCONTRADO: RefId={refId}, Profit={profit}");
                // Listar IDs atuais para debug
                var allIds = string.Join(", ", _purchaseTransactions.Select(t => t.RefId));
                System.Diagnostics.Debug.WriteLine($"IDs disponíveis: {allIds}");
            }
        }
    }
    
    // Método para atualizar por proximidade de hora, quando IDs não são confiáveis
    public void UpdateProfitFromApiByTime(DateTime apiDateTime, decimal profit, int secondsWindow = 30)
    {
        System.Diagnostics.Debug.WriteLine($"TENTANDO MATCH POR HORA: API DateTime={apiDateTime}, Profit={profit}");
        
        lock(_lock)
        {
            // Verificar transações que têm horário de compra próximo ao horário da API
            var startWindow = apiDateTime.AddSeconds(-secondsWindow);
            var endWindow = apiDateTime.AddSeconds(secondsWindow);
            
            var matchingTransaction = _purchaseTransactions
                .Where(t => t.BuyTime.ToUniversalTime() >= startWindow && 
                           t.BuyTime.ToUniversalTime() <= endWindow)
                .OrderBy(t => Math.Abs((t.BuyTime.ToUniversalTime() - apiDateTime).TotalSeconds))
                .FirstOrDefault();
                
            if (matchingTransaction != null)
            {
                System.Diagnostics.Debug.WriteLine($"MATCH POR HORA ENCONTRADO: RefId={matchingTransaction.RefId}, " +
                    $"BuyTime={matchingTransaction.BuyTime.ToUniversalTime()}, API DateTime={apiDateTime}, " +
                    $"P/L anterior={matchingTransaction.TotalProfitLoss}, novo P/L={profit}");
                
                matchingTransaction.TotalProfitLoss = profit;
                matchingTransaction.ProfitFromApi = true;
                // Forçar atualização da UI
                matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLoss));
                matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLossString));
                
                // Notificar mudança no total geral
                OnPropertyChanged(nameof(TotalProfitLoss));
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"NENHUM MATCH POR HORA: API DateTime={apiDateTime}, janela={startWindow} a {endWindow}");
            }
        }
    }

    public void ClearTransactions()
    {
        // Garantir que a limpeza seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => ClearTransactions());
            return;
        }

        lock (_lock)
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE] Limpando {_purchaseTransactions.Count} transações de compra");
            _purchaseTransactions.Clear();
            _nextTransactionId = 1; // Reset do contador de IDs
            
            // Reset do martingale
            _currentMartingaleLevel = 0;
            _maxMartingaleLevelReached = 0;
            _totalStakeUsedInMaxLevel = 0;
            _baseStake = 0.35m; // Resetar para valor padrão

            // Reset dos contadores da sequência atual
            _currentPurchaseSequenceStakeSum = 0;
            _currentPurchaseSequenceLength = 0;

            System.Diagnostics.Debug.WriteLine($"[PURCHASE] Tabela de compras limpa e martingale resetado");
        }
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(PurchaseTransactions));
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
        OnPropertyChanged(nameof(TotalStakeUsed));
    }

    public void SetMoneyManagement(MoneyManagementViewModel moneyManagement)
    {
        _moneyManagement = moneyManagement;
        System.Diagnostics.Debug.WriteLine($"[PURCHASE] MoneyManagement configurado para compras");
    }
    
    public void SetProposalViewModel(ProposalViewModel proposalViewModel)
    {
        _proposalViewModel = proposalViewModel;
        System.Diagnostics.Debug.WriteLine($"[PURCHASE] ProposalViewModel configurado para compras");
    }
    
    public void ProcessTransactionResult(ContractTransaction transaction)
    {
        // Verificar se foi vitória ou derrota
        bool isWin = transaction.TotalProfitLoss > 0;
        
        // Validação dupla: verificar se realmente deveria ganhar baseado nos spots
        bool shouldWin = transaction.IsCurrentlyWinning(transaction.EndSpot);
        
        System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Processando resultado: RefId={transaction.RefId}, P/L={transaction.TotalProfitLoss:F2}, IsWin={isWin}, ShouldWin={shouldWin}");
        
        if (isWin != shouldWin)
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] ⚠️ INCONSISTÊNCIA: P/L indica {(isWin ? "VITÓRIA" : "DERROTA")}, mas lógica indica {(shouldWin ? "VITÓRIA" : "DERROTA")}");
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] StartSpot: {transaction.StartSpot:F5}, EndSpot: {transaction.EndSpot:F5}, Type: {transaction.Type}");
            
            // Usar a verificação mais confiável (baseada nos spots)
            isWin = shouldWin;
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Usando resultado baseado nos spots: {(isWin ? "VITÓRIA" : "DERROTA")}");
        }
        
        if (isWin)
        {
            // Vitória: resetar martingale
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] ✅ VITÓRIA CONFIRMADA - Resetando martingale");
            ResetMartingale();
        }
        else
        {
            // Derrota: aplicar martingale
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] ❌ DERROTA CONFIRMADA - Aplicando martingale");
            ApplyMartingale();
        }
    }
    
    private void ResetMartingale()
    {
        if (_currentMartingaleLevel > 0)
        {
            System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Resetando martingale do nível {_currentMartingaleLevel} para 0");

            // Registrar a sequência de martingale que está terminando
            RegisterPurchaseMartingaleSequence();

            _currentMartingaleLevel = 0;
            CurrentMartingaleLevel = 0;

            // Restaurar stake base no ProposalViewModel
            if (_proposalViewModel != null)
            {
                _proposalViewModel.StakeText = _baseStake.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Stake restaurado para base: {_baseStake:F2}");
            }
        }
        else
        {
            // Mesmo se o nível for 0, resetar contadores da sequência
            _currentPurchaseSequenceStakeSum = 0;
            _currentPurchaseSequenceLength = 0;
        }
    }
    
    private void ApplyMartingale()
    {
        if (_moneyManagement != null && _moneyManagement.MartingaleEnabled)
        {
            // Verificar se não excedeu o nível máximo
            if (_currentMartingaleLevel < _moneyManagement.Level)
            {
                _currentMartingaleLevel++;
                CurrentMartingaleLevel = _currentMartingaleLevel;
                
                // Atualizar estatísticas
                if (_currentMartingaleLevel > _maxMartingaleLevelReached)
                {
                    MaxMartingaleLevelReached = _currentMartingaleLevel;
                    
                    // Calcular total de stakes usado neste nível
                    decimal baseStake = 0.35m; // Obter do ProposalViewModel se disponível
                    if (_proposalViewModel != null && decimal.TryParse(_proposalViewModel.StakeText, out var proposalStake))
                    {
                        baseStake = proposalStake;
                    }
                    
                    decimal totalStake = 0;
                    decimal currentStake = baseStake;
                    for (int i = 0; i <= _currentMartingaleLevel; i++)
                    {
                        totalStake += currentStake;
                        if (i < _currentMartingaleLevel)
                        {
                            currentStake *= _moneyManagement.Factor;
                        }
                    }
                    TotalStakeUsedInMaxLevel = totalStake;
                }
                
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Aplicando martingale - Nível: {_currentMartingaleLevel}/{_moneyManagement.Level}");
                
                // Atualizar stake no ProposalViewModel
                if (_proposalViewModel != null)
                {
                    // Obter stake base do ProposalViewModel apenas uma vez (quando level = 0)
                    if (_currentMartingaleLevel == 0 && decimal.TryParse(_proposalViewModel.StakeText, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var proposalStake))
                    {
                        _baseStake = proposalStake;
                    }
                    
                    // Calcular novo stake baseado no nível atual: base * factor^level
                    decimal newStake = _baseStake;
                    for (int i = 0; i < _currentMartingaleLevel; i++)
                    {
                        newStake *= _moneyManagement.Factor;
                    }
                    
                    _proposalViewModel.StakeText = newStake.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                    System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] Stake calculado - Base: {_baseStake:F2}, Nível: {_currentMartingaleLevel}, Factor: {_moneyManagement.Factor}, Resultado: {newStake:F2}");
                }
            }
            else
            {
                // Atingiu o nível máximo e perdeu novamente - RESETAR IMEDIATAMENTE
                System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] ⚠️ NÍVEL MÁXIMO ATINGIDO E PERDEU ({_moneyManagement.Level}) - RESETANDO IMEDIATAMENTE");

                // Registrar a sequência antes de resetar
                RegisterPurchaseMartingaleSequence();

                // Reset completo
                _currentMartingaleLevel = 0;
                CurrentMartingaleLevel = 0;
                _currentPurchaseSequenceStakeSum = 0;
                _currentPurchaseSequenceLength = 0;

                // Restaurar stake base no ProposalViewModel
                if (_proposalViewModel != null)
                {
                    _proposalViewModel.StakeText = _baseStake.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                    System.Diagnostics.Debug.WriteLine($"[PURCHASE-MARTINGALE] ✅ Reset após atingir nível máximo - Próxima stake será base: {_baseStake:F2}");
                }
            }
        }
    }

    public void Dispose()
    {
        _expirationTimer?.Stop();
        _expirationTimer?.Dispose();
        
        lock (_lock)
        {
            _purchaseTransactions.Clear();
        }
        
        GC.SuppressFinalize(this);
    }
} 