using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using Excalibur.Models;
using System.Timers;
using Excalibur.Core.Interfaces;
using Microsoft.Extensions.Logging;
using Excalibur.Services;
using System.Globalization;

namespace Excalibur.ViewModels;

public class SimulationViewModel : BaseViewModel, IDisposable
{
    private ObservableCollection<ContractTransaction> _simulationTransactions;
    private int _nextTransactionId = 1;
    private System.Timers.Timer _expirationTimer;
    private decimal _currentSpot;
    private readonly object _lock = new object();

    // Sistema simples de contagem
    private int _simulationWins = 0;
    private int _simulationLosses = 0;
    
    // Constantes para gestão de recursos
    private const int MAX_SIMULATION_TRANSACTIONS = 1000;
    private const int CLEANUP_THRESHOLD = 100; // Limpar a cada 100 transações
    private const int TRANSACTIONS_TO_KEEP = 300; // Manter apenas as últimas 300
    
    // Controle de martingale para simulação
    private int _currentMartingaleLevel = 0;
    private decimal _baseStake = 0.35m; // Stake inicial padrão
    
    // Estatísticas de martingale
    private int _simulationMaxMartingaleLevel = 0;
    private decimal _simulationTotalStakeUsed = 0;

    // Controle da sequência atual de martingale
    private decimal _currentSequenceStakeSum = 0;
    private int _currentSequenceLength = 0;

    // Event para trigger de compra real
    public event EventHandler? TriggerRealPurchase;

    // Sistema de martingale para simulação
    private readonly IMartingaleService _simulationMartingaleService;
    private MoneyManagementViewModel? _moneyManagement;
    private PurchaseViewModel? _purchaseViewModel;

    // Sistema de martingale independente para compras reais
    private decimal _realPurchaseBaseStake = 0.35m;
    private int _realPurchaseCurrentLevel = 0;
    private decimal _realPurchaseCurrentStake = 0.35m;

    // Propriedade pública para acessar a stake das compras reais
    public decimal RealPurchaseStake => _realPurchaseCurrentStake;
    public int RealPurchaseLevel => _realPurchaseCurrentLevel;
    
    // Referência para o ProposalViewModel para obter stake atual
    private ProposalViewModel? _proposalViewModel;

    public SimulationViewModel(IMartingaleService martingaleService)
    {
        _simulationMartingaleService = martingaleService;
        _simulationTransactions = new ObservableCollection<ContractTransaction>();
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] SimulationViewModel criado - SimulationTransactions inicializada com Count={_simulationTransactions.Count}");
        
        // Timer para verificar expiração de contratos simulados
        _expirationTimer = new System.Timers.Timer(1000);
        _expirationTimer.Elapsed += CheckSimulationExpirations;
        _expirationTimer.AutoReset = true;
        _expirationTimer.Start();
    }

    public ObservableCollection<ContractTransaction> SimulationTransactions
    {
        get => _simulationTransactions;
        set
        {
            _simulationTransactions = value;
            OnPropertyChanged();
        }
    }

    // Propriedades para contagem simples
    public int SimulationWins 
    { 
        get => _simulationWins; 
        private set 
        { 
            _simulationWins = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(SimulationStatus));
        } 
    }

    public int SimulationLosses 
    { 
        get => _simulationLosses; 
        private set 
        { 
            _simulationLosses = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(SimulationStatus));
        } 
    }

    public string SimulationStatus => $"W: {_simulationWins} | L: {_simulationLosses}";

    // Propriedade para contar total de entradas na simulação
    public int TotalSimulationEntries => _simulationTransactions?.Count ?? 0;
    
    // Propriedades para estatísticas de martingale
    public int SimulationMaxMartingaleLevel
    {
        get => _simulationMaxMartingaleLevel;
        private set
        {
            _simulationMaxMartingaleLevel = value;
            OnPropertyChanged();
        }
    }
    
    public decimal SimulationTotalStakeUsed
    {
        get => _simulationTotalStakeUsed;
        private set
        {
            _simulationTotalStakeUsed = value;
            OnPropertyChanged();
        }
    }
    
    public int CurrentMartingaleLevel
    {
        get => _currentMartingaleLevel;
        private set
        {
            _currentMartingaleLevel = value;
            OnPropertyChanged();
        }
    }

    public void AddTransaction(ContractTransaction transaction)
    {
        // Garantir que a adição seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => AddTransaction(transaction));
            return;
        }

        lock (_lock)
        {
            // Gerar ID único se não fornecido
            if (transaction.Id == 0)
            {
                transaction.Id = _nextTransactionId++;
            }

            // Garantir que seja marcado como ativo para simulação
            transaction.IsActive = true; // Marcar como ativo para simulação

            // IMPORTANTE: Verificar se há transações que ganharam recentemente e resetar martingale se necessário
            CheckForRecentWinsAndReset();

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🎯 CRIANDO TRANSAÇÃO {transaction.Id} - Nível martingale ANTES do cálculo: {_currentMartingaleLevel}");

            // Calcular stake baseado no estado atual do martingale
            transaction.Stake = CalculateCurrentStake();

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🎯 TRANSAÇÃO {transaction.Id} CRIADA - Stake: {transaction.Stake:F2}, Nível final: {_currentMartingaleLevel}");

            // SEMPRE acumular stake na sequência atual (incluindo a primeira que pode virar martingale)
            _currentSequenceStakeSum += transaction.Stake;
            _currentSequenceLength++;

            if (_currentMartingaleLevel > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 ACUMULANDO STAKE MARTINGALE - Stake: {transaction.Stake:F2}, Nível: {_currentMartingaleLevel}, Soma atual: {_currentSequenceStakeSum:F2}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 ACUMULANDO STAKE BASE - Stake: {transaction.Stake:F2}, Nível: {_currentMartingaleLevel}, Soma atual: {_currentSequenceStakeSum:F2} (pode virar martingale)");
            }

            // Calcular payout baseado no stake atual
            transaction.Payout = CalculatePayoutFromProposal(transaction.Stake);

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Stake calculado: {transaction.Stake:F2}, Payout: {transaction.Payout:F2}, Nível: {_currentMartingaleLevel}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Sequência atual - Soma: {_currentSequenceStakeSum:F2}, Comprimento: {_currentSequenceLength}");
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= ADICIONANDO NOVA SIMULAÇÃO =======");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ANTES: SimulationTransactions.Count = {_simulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Nova transação:");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - ID: {transaction.Id}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Type: {transaction.Type}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - RefId: {transaction.RefId}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - BuyTime: {transaction.BuyTime:HH:mm:ss.fff}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Stake: {transaction.Stake}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Payout: {transaction.Payout}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Duration: {transaction.Duration} {transaction.DurationType}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - IsActive: {transaction.IsActive}");
            
            var expectedDuration = transaction.GetExpirationTimeInSeconds();
            var expectedExpiration = transaction.BuyTime.AddSeconds(expectedDuration);
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Duração calculada: {expectedDuration}s");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Expiração esperada: {expectedExpiration:HH:mm:ss.fff}");
            
            try
            {
                _simulationTransactions.Add(transaction);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ERRO ao adicionar transação: {ex.Message}");
                throw;
            }
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] DEPOIS: SimulationTransactions.Count = {_simulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= FIM DA ADIÇÃO =======");
            
            // Verificar se precisa fazer limpeza periódica
            CheckAndCleanupTransactions();
        }
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(SimulationTransactions));
        OnPropertyChanged(nameof(TotalSimulationEntries));
        
        // Verificar limite máximo de transações
        CheckTransactionLimit();
    }

    public void UpdateTransaction(string refId, decimal endSpot, decimal totalProfitLoss, bool isClosed = false)
    {
        lock (_lock)
        {
            var transaction = _simulationTransactions.FirstOrDefault(t => t.RefId == refId);
            if (transaction != null)
            {
                transaction.EndSpot = endSpot;
                
                if (isClosed)
                {
                    transaction.SellTime = DateTime.Now;
                    transaction.IsActive = false;
                    
                    // Recalcular o P/L correto baseado na lógica de win/loss
                    CalculateFinalPayout(transaction);
                    
                    // Avaliar resultado e atualizar contadores
                    EvaluateSimulationResult(transaction);
                }
                else
                {
                    // Para contratos ativos, usar o P/L passado como parâmetro
                    transaction.TotalProfitLoss = totalProfitLoss;
                }
            }
        }
    }

    public void UpdateCurrentSpot(decimal currentSpot)
    {
        _currentSpot = currentSpot;
        
        lock (_lock)
        {
            // Atualizar contratos simulados ativos
            foreach (var transaction in _simulationTransactions.Where(t => t.IsActive))
            {
                transaction.EndSpot = currentSpot;
                
                // Verificar se está ganhando em tempo real
                if (transaction.StartSpot > 0)
                {
                    bool isWinning = CheckIfWin(transaction.Type, transaction.StartSpot, currentSpot);
                    
                    // Log detalhado para diagnóstico
                    System.Diagnostics.Debug.WriteLine($"[SIMULATION-RT] ID={transaction.Id}, Type={transaction.Type}, " +
                        $"StartSpot={transaction.StartSpot:F3}, CurrentSpot={currentSpot:F3}, " +
                        $"Diff={(currentSpot - transaction.StartSpot):F3}, IsWinning={isWinning}");
                    
                    if (isWinning)
                    {
                        // Vitória: payout - stake
                        decimal profit = transaction.Payout - transaction.Stake;
                        transaction.TotalProfitLoss = profit;
                        System.Diagnostics.Debug.WriteLine($"[SIMULATION-RT] ✅ GANHANDO: Payout={transaction.Payout:F2}, Stake={transaction.Stake:F2}, P/L={profit:F2}");

                        // IMPORTANTE: Reset imediato do martingale quando detecta vitória em tempo real
                        // Isso garante que a próxima transação use o stake correto
                        if (_currentMartingaleLevel > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"[SIMULATION-RT] 🔄 RESET IMEDIATO - Transação {transaction.Id} está ganhando, resetando martingale do nível {_currentMartingaleLevel} para 0");
                            ResetMartingale();
                        }
                    }
                    else
                    {
                        // Derrota: perder o stake
                        transaction.TotalProfitLoss = -transaction.Stake;
                        System.Diagnostics.Debug.WriteLine($"[SIMULATION-RT] ❌ PERDENDO: Stake={transaction.Stake:F2}, P/L={-transaction.Stake:F2}");
                    }
                }
            }
        }
    }

    public void UpdateSampleConfiguration(int amostra, int wins, int losses)
    {
        // Agora usa os parâmetros da amostra para controlar quando disparar compras reais
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Configuração da amostra atualizada: Amostra={amostra}, Wins={wins}, Losses={losses}");
        OnPropertyChanged(nameof(SimulationStatus));
    }

    public void SetMoneyManagement(MoneyManagementViewModel moneyManagement)
    {
        _moneyManagement = moneyManagement;
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] MoneyManagement configurado para simulação");
    }
    
    public void SetPurchaseViewModel(PurchaseViewModel purchaseViewModel)
    {
        _purchaseViewModel = purchaseViewModel;
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] PurchaseViewModel configurado para simulação");
    }
    
    public void SetProposalViewModel(ProposalViewModel proposalViewModel)
    {
        _proposalViewModel = proposalViewModel;
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ProposalViewModel configurado para simulação");
    }


    private void CheckSimulationExpirations(object? sender, ElapsedEventArgs e)
    {
        var now = DateTime.Now;
        
        lock (_lock)
        {
            // Debug: verificar se há transações ativas
            var activeTransactions = _simulationTransactions.Where(t => t.IsActive).ToList();
            var totalTransactions = _simulationTransactions.Count;
            
            if (totalTransactions > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Timer tick: {totalTransactions} transações total, {activeTransactions.Count} ativas");
                
                // Debug: listar todas as transações ativas
                foreach (var tx in activeTransactions)
                {
                    var timeElapsed = (now - tx.BuyTime).TotalSeconds;
                    var expirationTime = tx.GetExpirationTimeInSeconds();
                    System.Diagnostics.Debug.WriteLine($"[SIMULATION] Transação ativa: ID={tx.Id}, RefId={tx.RefId}, Elapsed={timeElapsed:F1}s, Required={expirationTime}s, Restante={expirationTime - timeElapsed:F1}s");
                }
            }
            
            var expiredTransactions = _simulationTransactions
                .Where(t => t.IsActive)
                .Where(t =>
                {
                    var timeElapsed = (now - t.BuyTime).TotalSeconds;
                    var expirationTime = t.GetExpirationTimeInSeconds();
                    var isExpired = timeElapsed >= expirationTime;
                    
                    if (isExpired)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⏰ CONTRATO EXPIRANDO: BuyTime={t.BuyTime:HH:mm:ss}, " +
                            $"Now={now:HH:mm:ss}, Elapsed={timeElapsed:F1}s, Required={expirationTime}s");
                    }
                    
                    return isExpired;
                })
                .ToList();

            foreach (var transaction in expiredTransactions)
            {
                transaction.EndSpot = _currentSpot;
                
                // Calcular SellTime exato baseado no BuyTime + duração para precisão
                var exactSellTime = transaction.BuyTime.AddSeconds(transaction.GetExpirationTimeInSeconds());
                transaction.SellTime = exactSellTime;
                transaction.IsActive = false;
                
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ========== CONTRATO EXPIRANDO ==========");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ID={transaction.Id}, RefId={transaction.RefId}, Type={transaction.Type}");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] StartSpot={transaction.StartSpot:F5}, EndSpot={transaction.EndSpot:F5}, CurrentSpot={_currentSpot:F5}");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Variação: {transaction.EndSpot - transaction.StartSpot:F5} ({(transaction.EndSpot > transaction.StartSpot ? "SUBIDA" : "DESCIDA")})");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] SellTime calculado: BuyTime={transaction.BuyTime:HH:mm:ss} + {transaction.GetExpirationTimeInSeconds()}s = {exactSellTime:HH:mm:ss}");
                
                // Debug adicional para verificar lógica de win/loss
                var shouldWin = transaction.Type.ToLower().Contains("higher") ? transaction.EndSpot > transaction.StartSpot : transaction.EndSpot < transaction.StartSpot;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Deveria ganhar: {shouldWin} (Type: {transaction.Type})");
                
                // Calcular resultado final usando a mesma lógica do PurchaseViewModel
                CalculateFinalPayout(transaction);
                
                // Avaliar resultado
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🎯 Avaliando resultado da transação expirada: ID={transaction.Id}");
                EvaluateSimulationResult(transaction);
                
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Contrato simulado expirado: ID={transaction.Id}, Result={transaction.TotalProfitLoss}");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ========== FIM DA EXPIRAÇÃO ==========");
            }
        }
    }

    private void CalculateFinalPayout(ContractTransaction transaction)
    {
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ====== CALCULANDO PAYOUT FINAL ======");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] RefId: {transaction.RefId}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Tipo: '{transaction.Type}'");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] StartSpot: {transaction.StartSpot:F5}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] EndSpot: {transaction.EndSpot:F5}");
        
        // LÓGICA SIMPLES: Comparação direta Higher/Lower
        bool isWin = CheckIfWin(transaction.Type, transaction.StartSpot, transaction.EndSpot);
        
        if (isWin)
        {
            // Vitória: usar payout da transação
            transaction.TotalProfitLoss = transaction.Payout - transaction.Stake;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ✅ VITÓRIA - P/L: {transaction.TotalProfitLoss:F2}");
        }
        else
        {
            // Derrota: perder o stake
            transaction.TotalProfitLoss = -transaction.Stake;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ❌ DERROTA - P/L: {transaction.TotalProfitLoss:F2}");
        }
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ====== FIM DO CÁLCULO ======");
    }
    
    private bool CheckIfWin(string contractType, decimal startSpot, decimal endSpot)
    {
        string type = contractType.ToLower().Trim();
        
        System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] Verificando: {contractType}, Start: {startSpot:F5}, End: {endSpot:F5}");
        
        // LÓGICA ROBUSTA:
        // Higher/Rise/Up/Call ganha se EndSpot > StartSpot
        // Lower/Fall/Down/Put ganha se EndSpot < StartSpot
        
        // Contratos de subida
        if (type.Contains("higher") || type.Contains("rise") || 
            type.Contains("up") || type.Contains("call"))
        {
            bool result = endSpot > startSpot;
            decimal diff = endSpot - startSpot;
            System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] HIGHER: {endSpot:F5} > {startSpot:F5} = {result} (diff: {diff:F5})");
            return result;
        }
        
        // Contratos de descida
        if (type.Contains("lower") || type.Contains("fall") || 
            type.Contains("down") || type.Contains("put"))
        {
            bool result = endSpot < startSpot;
            decimal diff = startSpot - endSpot;
            System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] LOWER: {endSpot:F5} < {startSpot:F5} = {result} (diff: {diff:F5})");
            return result;
        }
        
        // Contratos de igualdade
        if (type.Contains("matches") || type.Contains("equals"))
        {
            // Verificar último dígito
            var startDigit = GetLastDigit(startSpot);
            var endDigit = GetLastDigit(endSpot);
            bool result = startDigit == endDigit;
            System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] MATCHES: {startDigit} == {endDigit} = {result}");
            return result;
        }
        
        // Contratos de diferença
        if (type.Contains("differs") || type.Contains("not equal"))
        {
            // Verificar último dígito
            var startDigit = GetLastDigit(startSpot);
            var endDigit = GetLastDigit(endSpot);
            bool result = startDigit != endDigit;
            System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] DIFFERS: {startDigit} != {endDigit} = {result}");
            return result;
        }
        
        // Default: Higher (caso não reconheça o tipo)
        bool defaultResult = endSpot > startSpot;
        System.Diagnostics.Debug.WriteLine($"[WIN-CHECK] TIPO NÃO RECONHECIDO - Usando Higher como padrão: {endSpot:F5} > {startSpot:F5} = {defaultResult}");
        return defaultResult;
    }
    
    private int GetLastDigit(decimal value)
    {
        // Obter o último dígito do valor
        var stringValue = value.ToString("F5", CultureInfo.InvariantCulture);
        var lastChar = stringValue.Replace(".", "").Replace(",", "").Last();
        return int.Parse(lastChar.ToString());
    }

    private void EvaluateSimulationResult(ContractTransaction transaction)
    {
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Avaliando resultado: RefId={transaction.RefId}, P/L={transaction.TotalProfitLoss:F2}");
        
        bool isWin = transaction.TotalProfitLoss > 0;
        
        // Validação dupla: verificar se realmente deveria ganhar baseado nos spots
        bool shouldWin = CheckIfWin(transaction.Type, transaction.StartSpot, transaction.EndSpot);

        if (isWin != shouldWin)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ INCONSISTÊNCIA: P/L indica {(isWin ? "VITÓRIA" : "DERROTA")}, mas lógica indica {(shouldWin ? "VITÓRIA" : "DERROTA")}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] StartSpot: {transaction.StartSpot:F5}, EndSpot: {transaction.EndSpot:F5}, Type: {transaction.Type}");

            // CORREÇÃO: Usar a lógica baseada nos spots que é mais confiável
            isWin = shouldWin;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔧 CORRIGINDO: Usando resultado baseado nos spots: {(isWin ? "VITÓRIA" : "DERROTA")}");
        }

        // Usar o resultado corrigido
        bool finalWin = isWin;

        System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔍 DECISÃO FINAL: isWin={isWin}, shouldWin={shouldWin}, finalWin={finalWin}");
        
        if (finalWin)
        {
            SimulationWins++;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🟢 VITÓRIA CONFIRMADA! Total wins: {SimulationWins}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🔄 VITÓRIA TRANSAÇÃO {transaction.Id} - Nível antes do reset: {_currentMartingaleLevel}");

            // Vitória: resetar martingale
            ResetMartingale();

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🔄 RESET CONCLUÍDO TRANSAÇÃO {transaction.Id} - Nível após reset: {_currentMartingaleLevel}");

            // Verificar se as condições da amostra foram atingidas após esta vitória
            CheckSampleConditionsAndTriggerRealPurchase();
        }
        else
        {
            SimulationLosses++;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔴 DERROTA! Total losses: {SimulationLosses}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🔴 DERROTA TRANSAÇÃO {transaction.Id} - Nível antes do martingale: {_currentMartingaleLevel}");

            // Derrota: aplicar martingale para próxima entrada
            ApplyMartingale();

            // Verificar se as condições da amostra foram atingidas após esta derrota
            CheckSampleConditionsAndTriggerRealPurchase();

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-CRITICAL] 🔴 MARTINGALE APLICADO TRANSAÇÃO {transaction.Id} - Nível após martingale: {_currentMartingaleLevel}");
        }
    }
    
    private void CheckForRecentWinsAndReset()
    {
        // Verificar se há transações recentes que ganharam e ainda não foram processadas
        var recentWinningTransactions = _simulationTransactions
            .Where(t => !t.IsActive && t.TotalProfitLoss > 0 && t.SellTime.HasValue)
            .OrderByDescending(t => t.SellTime)
            .Take(1)
            .ToList();

        if (recentWinningTransactions.Count > 0)
        {
            var lastWin = recentWinningTransactions.First();
            var timeSinceWin = DateTime.Now - lastWin.SellTime!.Value;

            // Se houve uma vitória recente (últimos 5 segundos) e martingale ainda não foi resetado
            if (timeSinceWin.TotalSeconds <= 5 && _currentMartingaleLevel > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-PREVENTIVE] 🔄 Vitória recente detectada (ID: {lastWin.Id}, há {timeSinceWin.TotalSeconds:F1}s) - Resetando martingale preventivamente");
                ResetMartingale();
            }
        }
    }

    private void ResetMartingale()
    {
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Reset chamado - Nível atual: {_currentMartingaleLevel}");

        if (_currentMartingaleLevel > 0)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Resetando martingale do nível {_currentMartingaleLevel} para 0 (volta ao stake base)");

            // Registrar a sequência de martingale que está terminando
            RegisterMartingaleSequence();

            _currentMartingaleLevel = 0;
            CurrentMartingaleLevel = 0;
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Reset concluído - Novo nível: {_currentMartingaleLevel}, Stake resetada para: {_baseStake:F2}");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Nível já era 0 - Nenhuma ação necessária");
        }

        // Resetar contadores da sequência atual
        _currentSequenceStakeSum = 0;
        _currentSequenceLength = 0;
    }

    private void RegisterMartingaleSequence()
    {
        // Só registrar se houve uma sequência de martingale REAL (chegou a nível > 0)
        // E só se a sequência teve pelo menos 2 transações (uma perdeu, outra ganhou)
        if (_currentSequenceLength >= 2 && _currentSequenceStakeSum > 0 && _currentMartingaleLevel > 0)
        {
            // Verificar se esta sequência é maior que a anterior
            if (_currentSequenceStakeSum > _simulationTotalStakeUsed)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 NOVA MAIOR SEQUÊNCIA MARTINGALE - Anterior: {_simulationTotalStakeUsed:F2}, Nova: {_currentSequenceStakeSum:F2}");
                _simulationTotalStakeUsed = _currentSequenceStakeSum;
                SimulationTotalStakeUsed = _simulationTotalStakeUsed;

                // Atualizar também o nível máximo se necessário
                if (_currentMartingaleLevel > _simulationMaxMartingaleLevel)
                {
                    _simulationMaxMartingaleLevel = _currentMartingaleLevel;
                    SimulationMaxMartingaleLevel = _simulationMaxMartingaleLevel;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 Sequência martingale menor - Atual: {_currentSequenceStakeSum:F2}, Mantendo: {_simulationTotalStakeUsed:F2}");
            }

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Sequência martingale registrada - Comprimento: {_currentSequenceLength}, Soma: {_currentSequenceStakeSum:F2}, Total registrado: {_simulationTotalStakeUsed:F2}");
        }
        else if (_currentSequenceLength == 1)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 Sequência única (sem martingale) - Stake: {_currentSequenceStakeSum:F2} - NÃO registrada no Total Stake");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] 📊 Sequência inválida para registro - Comprimento: {_currentSequenceLength}, Nível: {_currentMartingaleLevel}");
        }
    }
    
    private void ApplyMartingale()
    {
        if (_moneyManagement != null && _moneyManagement.MartingaleEnabled)
        {
            // Incrementar o nível do martingale após uma perda
            if (_currentMartingaleLevel < _moneyManagement.Level)
            {
                _currentMartingaleLevel++;
                CurrentMartingaleLevel = _currentMartingaleLevel;

                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] Aplicando martingale após perda - Nível: {_currentMartingaleLevel}/{_moneyManagement.Level}");
            }
            else
            {
                // Atingiu o nível máximo e perdeu novamente - RESETAR IMEDIATAMENTE
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] ⚠️ NÍVEL MÁXIMO ATINGIDO E PERDEU ({_moneyManagement.Level}) - RESETANDO IMEDIATAMENTE");

                // Registrar a sequência antes de resetar
                RegisterMartingaleSequence();

                // Reset completo
                _currentMartingaleLevel = 0;
                CurrentMartingaleLevel = 0;
                _currentSequenceStakeSum = 0;
                _currentSequenceLength = 0;

                System.Diagnostics.Debug.WriteLine($"[SIMULATION-MARTINGALE] ✅ Reset após atingir nível máximo - Próxima stake será base: {_baseStake:F2}");
            }
        }
    }

    private void LogToFile(string message)
    {
        try
        {
            string logFile = "simulation_debug.log";
            string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - {message}\n";
            System.IO.File.AppendAllText(logFile, logEntry);
        }
        catch { /* Ignorar erros de arquivo */ }
    }

    // Métodos para gerenciar martingale das compras reais
    private void InitializeRealPurchaseMartingale()
    {
        if (_moneyManagement != null)
        {
            _realPurchaseBaseStake = _moneyManagement.BaseStake;
            _realPurchaseCurrentLevel = 0;
            _realPurchaseCurrentStake = _realPurchaseBaseStake;
            LogToFile($"REAL PURCHASE MARTINGALE: Inicializado - Base={_realPurchaseBaseStake:F2}, Level={_realPurchaseCurrentLevel}");
        }
    }

    private void ApplyRealPurchaseMartingale()
    {
        if (_moneyManagement == null) return;

        _realPurchaseCurrentLevel++;

        // Aplicar multiplicador do martingale usando Factor
        _realPurchaseCurrentStake = _realPurchaseBaseStake * (decimal)Math.Pow((double)_moneyManagement.Factor, _realPurchaseCurrentLevel);

        // Verificar se não excede o nível máximo configurado
        if (_realPurchaseCurrentLevel > _moneyManagement.Level)
        {
            _realPurchaseCurrentLevel = _moneyManagement.Level;
            _realPurchaseCurrentStake = _realPurchaseBaseStake * (decimal)Math.Pow((double)_moneyManagement.Factor, _realPurchaseCurrentLevel);
        }

        LogToFile($"REAL PURCHASE MARTINGALE: Aplicado - Level={_realPurchaseCurrentLevel}/{_moneyManagement.Level}, Stake={_realPurchaseCurrentStake:F2}");
    }

    private void ResetRealPurchaseMartingale()
    {
        _realPurchaseCurrentLevel = 0;
        _realPurchaseCurrentStake = _realPurchaseBaseStake;
        LogToFile($"REAL PURCHASE MARTINGALE: Reset - Level={_realPurchaseCurrentLevel}, Stake={_realPurchaseCurrentStake:F2}");
    }

    private void CalculateRealPurchaseStake()
    {
        try
        {
            // Inicializar martingale das compras reais se necessário
            if (_realPurchaseBaseStake <= 0)
            {
                InitializeRealPurchaseMartingale();
            }

            // Se não há transações de compra, usar stake base
            if (_purchaseViewModel?.PurchaseTransactions?.Count == 0)
            {
                _realPurchaseCurrentStake = _realPurchaseBaseStake;
                _realPurchaseCurrentLevel = 0;
                LogToFile($"REAL PURCHASE STAKE: Primeira compra - Stake={_realPurchaseCurrentStake:F2}");
                return;
            }

            // Analisar a última transação de compra para determinar se foi vitória ou derrota
            var lastPurchase = _purchaseViewModel?.PurchaseTransactions?
                .Where(t => !t.IsActive && t.SellTime.HasValue)
                .OrderByDescending(t => t.SellTime)
                .FirstOrDefault();

            if (lastPurchase == null)
            {
                // Não há transações finalizadas, usar stake base
                _realPurchaseCurrentStake = _realPurchaseBaseStake;
                _realPurchaseCurrentLevel = 0;
                LogToFile($"REAL PURCHASE STAKE: Nenhuma transação finalizada - Stake={_realPurchaseCurrentStake:F2}");
                return;
            }

            bool lastWasWin = lastPurchase.TotalProfitLoss > 0;
            LogToFile($"REAL PURCHASE STAKE: Última transação foi {(lastWasWin ? "VITÓRIA" : "DERROTA")} - P/L={lastPurchase.TotalProfitLoss:F2}");

            if (lastWasWin)
            {
                // Vitória: resetar martingale
                ResetRealPurchaseMartingale();
            }
            else
            {
                // Derrota: aplicar martingale
                ApplyRealPurchaseMartingale();
            }

            LogToFile($"REAL PURCHASE STAKE: Calculada - Level={_realPurchaseCurrentLevel}, Stake={_realPurchaseCurrentStake:F2}");
        }
        catch (Exception ex)
        {
            LogToFile($"Erro ao calcular stake para compra real: {ex.Message}");
            // Em caso de erro, usar stake base
            _realPurchaseCurrentStake = _realPurchaseBaseStake;
            _realPurchaseCurrentLevel = 0;
        }
    }

    private void CheckSampleConditionsAndTriggerRealPurchase()
    {
        try
        {
            LogToFile("INICIANDO CheckSampleConditionsAndTriggerRealPurchase()");
            LogToFile("MÉTODO EXECUTADO - TESTE DE LOG");

            if (_moneyManagement == null)
            {
                LogToFile("MoneyManagement não configurado - não verificando condições da amostra");
                return;
            }

            // Obter configurações da amostra
            int amostra = _moneyManagement.Amostra;
            int targetWins = _moneyManagement.Win;
            int targetLosses = _moneyManagement.Loss;

            LogToFile($"Verificando condições da amostra: Amostra={amostra}, TargetWins={targetWins}, TargetLosses={targetLosses}");

            // Pegar as últimas transações finalizadas (não ativas) para análise
            var recentTransactions = _simulationTransactions
                .Where(t => !t.IsActive && t.SellTime.HasValue)
                .OrderByDescending(t => t.SellTime)
                .Take(amostra)
                .ToList();

            if (recentTransactions.Count < amostra)
            {
                LogToFile($"Ainda não há transações suficientes para análise: {recentTransactions.Count}/{amostra}");
                return;
            }

            // Contar wins e losses na amostra
            int winsInSample = recentTransactions.Count(t => t.TotalProfitLoss > 0);
            int lossesInSample = recentTransactions.Count(t => t.TotalProfitLoss <= 0);

            LogToFile($"Análise da amostra: Wins={winsInSample}/{targetWins}, Losses={lossesInSample}/{targetLosses}");

            // Verificar se as condições foram atingidas
            bool shouldTriggerRealPurchase = false;
            string reason = "";

            if (winsInSample >= targetWins)
            {
                shouldTriggerRealPurchase = true;
                reason = $"Atingiu {winsInSample} vitórias (meta: {targetWins})";
            }
            else if (lossesInSample >= targetLosses)
            {
                shouldTriggerRealPurchase = true;
                reason = $"Atingiu {lossesInSample} derrotas (meta: {targetLosses})";
            }

            if (shouldTriggerRealPurchase)
            {
                LogToFile($"CONDIÇÕES DA AMOSTRA ATINGIDAS: {reason}");
                TriggerRealPurchaseIfPossible();
            }
            else
            {
                LogToFile("Condições ainda não atingidas - continuando simulação");
            }
        }
        catch (Exception ex)
        {
            LogToFile($"Erro ao verificar condições da amostra: {ex.Message}");
        }
    }

    private void TriggerRealPurchaseIfPossible()
    {
        try
        {
            LogToFile("Verificando se pode disparar compra real...");

            // Log do estado atual das tabelas
            var purchaseCount = _purchaseViewModel?.PurchaseTransactions?.Count ?? 0;
            var simulationCount = _simulationTransactions?.Count ?? 0;
            LogToFile($"Estado das tabelas: Compras={purchaseCount}, Simulação={simulationCount}");

            // Verificar se há operação ativa na tabela Compras
            if (HasActivePurchaseTransaction())
            {
                LogToFile("Já existe operação ativa na tabela Compras. Não disparando nova compra.");
                return;
            }

            // Calcular stake apropriada para compra real baseada no histórico de compras reais
            CalculateRealPurchaseStake();

            // Disparar compra real
            LogToFile($"TODAS AS CONDIÇÕES ATENDIDAS - Disparando compra real com stake={_realPurchaseCurrentStake:F2}");
            TriggerRealPurchase?.Invoke(this, EventArgs.Empty);
            LogToFile("Compra real disparada com sucesso!");
        }
        catch (Exception ex)
        {
            LogToFile($"Erro ao disparar compra real: {ex.Message}");
        }
    }
    
    private bool HasActivePurchaseTransaction()
    {
        if (_purchaseViewModel == null)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ PurchaseViewModel não configurado - permitindo compra");
            return false;
        }
        
        // Verificar se há transações ativas na tabela Compras
        var now = DateTime.Now;
        var activeTransactions = _purchaseViewModel.PurchaseTransactions?
            .Where(t => t.IsActive && !IsTransactionExpired(t, now))
            .ToList();
        int activeCount = activeTransactions?.Count ?? 0;
        
        // Verificar se há transações que deveriam estar expiradas mas ainda estão ativas
        var expiredButActive = _purchaseViewModel.PurchaseTransactions?
            .Where(t => t.IsActive && IsTransactionExpired(t, now))
            .ToList();
        
        if (expiredButActive?.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ Encontradas {expiredButActive.Count} transações expiradas mas ainda ativas:");
            foreach (var expired in expiredButActive)
            {
                var timeElapsed = (now - expired.BuyTime).TotalSeconds;
                var expectedDuration = expired.GetExpirationTimeInSeconds();
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] - ID: {expired.Id}, Elapsed: {timeElapsed:F1}s, Expected: {expectedDuration}s");
            }
        }
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Verificando transações ativas na tabela Compras: {activeCount}");
        
        if (activeCount > 0)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ Encontradas {activeCount} transações ativas na tabela Compras:");
            foreach (var transaction in activeTransactions!)
            {
                var timeElapsed = (DateTime.Now - transaction.BuyTime).TotalSeconds;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] - ID: {transaction.Id}, RefId: {transaction.RefId}, Type: {transaction.Type}, IsActive: {transaction.IsActive}, TimeElapsed: {timeElapsed:F1}s");
            }
            return true;
        }
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ✅ Nenhuma transação ativa na tabela Compras - pode disparar compra");
        return false;
    }
    
    private bool IsTransactionExpired(ContractTransaction transaction, DateTime now)
    {
        var timeElapsed = (now - transaction.BuyTime).TotalSeconds;
        var expectedDuration = transaction.GetExpirationTimeInSeconds();
        return timeElapsed >= expectedDuration;
    }
    
    private decimal CalculateCurrentStake()
    {
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] ===== CALCULANDO STAKE =====");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Nível atual: {_currentMartingaleLevel}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Base stake atual: {_baseStake:F2}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Martingale habilitado: {_moneyManagement?.MartingaleEnabled ?? false}");

        // Obter stake base do ProposalViewModel apenas quando não estamos em martingale
        if (_currentMartingaleLevel == 0 && _proposalViewModel != null && decimal.TryParse(_proposalViewModel.StakeText, NumberStyles.Any, CultureInfo.InvariantCulture, out var proposalStake))
        {
            _baseStake = proposalStake;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Base stake atualizado para: {_baseStake:F2}");
        }

        // Calcular stake baseado no nível atual do martingale
        if (_moneyManagement != null && _moneyManagement.MartingaleEnabled && _currentMartingaleLevel > 0)
        {
            // Verificação de segurança
            if (_currentMartingaleLevel > _moneyManagement.Level)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] ERRO: Nível {_currentMartingaleLevel} excede o máximo {_moneyManagement.Level}. Resetando.");
                ResetMartingale();
                return _baseStake;
            }

            // Calcular stake: base * factor^level
            decimal currentStake = _baseStake;
            for (int i = 0; i < _currentMartingaleLevel; i++)
            {
                currentStake *= _moneyManagement.Factor;
            }

            System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Calculando stake - Base: {_baseStake:F2}, Nível: {_currentMartingaleLevel}, Factor: {_moneyManagement.Factor}, Resultado: {currentStake:F2}");
            return currentStake;
        }

        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] Usando stake base: {_baseStake:F2} (nível 0 ou martingale desabilitado)");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION-STAKE] ===== FIM DO CÁLCULO =====");
        return _baseStake;
    }
    
    private decimal CalculatePayoutFromProposal(decimal stake)
    {
        // Tentar obter payout do ProposalViewModel
        if (_proposalViewModel != null && _proposalViewModel.Payout > 0)
        {
            // Calcular payout proporcional ao stake
            var proposalStake = decimal.TryParse(_proposalViewModel.StakeText, NumberStyles.Any, CultureInfo.InvariantCulture, out var pStake) ? pStake : _baseStake;
            var payoutRatio = _proposalViewModel.Payout / proposalStake;
            return stake * payoutRatio;
        }
        
        // Fallback: usar ratio padrão
        return stake * 2.0m;
    }

    public void ClearTransactions()
    {
        // Garantir que a limpeza seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => ClearTransactions());
            return;
        }

        lock (_lock)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Limpando {_simulationTransactions.Count} transações da simulação");
            _simulationTransactions.Clear();
            
            // Reset dos contadores
            SimulationWins = 0;
            SimulationLosses = 0;
            
            // Reset do sistema de martingale
            _currentMartingaleLevel = 0;
            CurrentMartingaleLevel = 0;
            
            // Reset das estatísticas de martingale
            _simulationMaxMartingaleLevel = 0;
            SimulationMaxMartingaleLevel = 0;
            _simulationTotalStakeUsed = 0;
            SimulationTotalStakeUsed = 0;
            
            // Reset do ID de transação
            _nextTransactionId = 1;
            
            // Resetar o stake base para o valor padrão
            _baseStake = 0.35m;
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Simulação completamente resetada - Wins: {SimulationWins}, Losses: {SimulationLosses}, Martingale Level: {CurrentMartingaleLevel}");
        }
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(SimulationTransactions));
        OnPropertyChanged(nameof(TotalSimulationEntries));
    }

    private void CheckAndCleanupTransactions()
    {
        try
        {
            var totalTransactions = _simulationTransactions.Count;
            
            // Fazer limpeza a cada CLEANUP_THRESHOLD transações
            if (totalTransactions > 0 && totalTransactions % CLEANUP_THRESHOLD == 0)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Iniciando limpeza periódica. Total: {totalTransactions}");
                CleanupOldTransactions();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Erro durante verificação de limpeza: {ex.Message}");
        }
    }
    
    private void CleanupOldTransactions()
    {
        try
        {
            var totalTransactions = _simulationTransactions.Count;
            if (totalTransactions <= TRANSACTIONS_TO_KEEP)
                return;

            // Remover as transações mais antigas (manter apenas as últimas TRANSACTIONS_TO_KEEP)
            var transactionsToRemove = _simulationTransactions
                .Where(t => !t.IsActive) // Apenas transações inativas
                .OrderBy(t => t.BuyTime)
                .Take(totalTransactions - TRANSACTIONS_TO_KEEP)
                .ToList();

            foreach (var transaction in transactionsToRemove)
            {
                _simulationTransactions.Remove(transaction);
            }

            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Limpeza concluída. Removidas {transactionsToRemove.Count} transações antigas. Total restante: {_simulationTransactions.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Erro durante limpeza de transações antigas: {ex.Message}");
        }
    }
    
    private void CheckTransactionLimit()
    {
        try
        {
            var totalTransactions = _simulationTransactions.Count;
            if (totalTransactions >= MAX_SIMULATION_TRANSACTIONS)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Limite de {MAX_SIMULATION_TRANSACTIONS} transações atingido. Parando adição de novas transações.");
                // Notificar que o limite foi atingido
                OnPropertyChanged(nameof(SimulationStatus));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Erro durante verificação de limite: {ex.Message}");
        }
    }
    
    public bool IsAtTransactionLimit => _simulationTransactions.Count >= MAX_SIMULATION_TRANSACTIONS;

    public void Dispose()
    {
        _expirationTimer?.Stop();
        _expirationTimer?.Dispose();
        
        lock (_lock)
        {
            _simulationTransactions.Clear();
        }
        
        GC.SuppressFinalize(this);
    }
} 