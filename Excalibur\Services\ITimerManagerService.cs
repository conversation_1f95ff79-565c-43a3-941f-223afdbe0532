using System.Collections.Concurrent;

namespace Excalibur.Services;

public interface ITimerManagerService : IDisposable
{
    string CreateTimer(TimeSpan interval, Func<CancellationToken, Task> callback, bool autoStart = true);
    void StartTimer(string timerId);
    void StopTimer(string timerId);
    void RemoveTimer(string timerId);
    bool IsTimerRunning(string timerId);
}

public class TimerManagerService : ITimerManagerService
{
    private readonly ConcurrentDictionary<string, ManagedTimer> _timers = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private bool _disposed = false;

    public string CreateTimer(TimeSpan interval, Func<CancellationToken, Task> callback, bool autoStart = true)
    {
        var timerId = Guid.NewGuid().ToString();
        var timer = new ManagedTimer(interval, callback, _cancellationTokenSource.Token);
        
        _timers[timerId] = timer;
        
        if (autoStart)
            timer.Start();
            
        return timerId;
    }

    public void StartTimer(string timerId)
    {
        if (_timers.TryGetValue(timerId, out var timer))
            timer.Start();
    }

    public void StopTimer(string timerId)
    {
        if (_timers.TryGetValue(timerId, out var timer))
            timer.Stop();
    }

    public void RemoveTimer(string timerId)
    {
        if (_timers.TryRemove(timerId, out var timer))
            timer.Dispose();
    }

    public bool IsTimerRunning(string timerId)
    {
        return _timers.TryGetValue(timerId, out var timer) && timer.IsRunning;
    }

    public void Dispose()
    {
        if (_disposed) return;

        _cancellationTokenSource.Cancel();
        
        foreach (var timer in _timers.Values)
            timer.Dispose();
            
        _timers.Clear();
        _cancellationTokenSource.Dispose();
        _disposed = true;
    }
}

internal class ManagedTimer : IDisposable
{
    private readonly TimeSpan _interval;
    private readonly Func<CancellationToken, Task> _callback;
    private readonly CancellationToken _globalCancellationToken;
    private CancellationTokenSource? _timerCancellationTokenSource;
    private Task? _timerTask;
    private bool _disposed = false;

    public bool IsRunning => _timerTask != null && !_timerTask.IsCompleted;

    public ManagedTimer(TimeSpan interval, Func<CancellationToken, Task> callback, CancellationToken globalCancellationToken)
    {
        _interval = interval;
        _callback = callback;
        _globalCancellationToken = globalCancellationToken;
    }

    public void Start()
    {
        if (_disposed || IsRunning) return;

        _timerCancellationTokenSource = new CancellationTokenSource();
        var combinedToken = CancellationTokenSource.CreateLinkedTokenSource(
            _globalCancellationToken, 
            _timerCancellationTokenSource.Token).Token;

        _timerTask = Task.Run(async () =>
        {
            while (!combinedToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(_interval, combinedToken);
                    if (!combinedToken.IsCancellationRequested)
                        await _callback(combinedToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Timer callback error: {ex.Message}");
                }
            }
        }, combinedToken);
    }

    public void Stop()
    {
        if (!IsRunning) return;

        _timerCancellationTokenSource?.Cancel();
        _timerTask?.Wait(TimeSpan.FromSeconds(5));
        _timerCancellationTokenSource?.Dispose();
        _timerCancellationTokenSource = null;
        _timerTask = null;
    }

    public void Dispose()
    {
        if (_disposed) return;

        Stop();
        _disposed = true;
    }
}