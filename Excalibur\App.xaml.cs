﻿using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Services;
using Excalibur.ViewModels;
using Excalibur.Services;
using System.Windows.Threading;

namespace Excalibur;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Registrar serviços
                services.AddSingleton<IDerivApiService, DerivApiService>();
                services.AddSingleton<Excalibur.Services.ISettingsService, Excalibur.Services.SettingsService>();
                
                // Registrar novos serviços refatorados
                services.AddSingleton<IMartingaleService, MartingaleService>();
                services.AddSingleton<IEventBusService, EventBusService>();
                services.AddSingleton<ITimerManagerService, TimerManagerService>();
                services.AddSingleton<ITransactionManagerService, TransactionManagerService>();
                services.AddSingleton<IThrottleService, ThrottleService>();
                services.AddSingleton<IAppLoggerService, AppLoggerService>();
                services.AddSingleton<IConfigurationManagerService, ConfigurationManagerService>();

                // Registrar ViewModels
                services.AddTransient<MainViewModel>();
                services.AddSingleton<AccountInfoViewModel>();
                services.AddSingleton<LogViewModel>();
                services.AddSingleton<ActiveSymbolsViewModel>();
                services.AddSingleton<ProposalViewModel>();
                services.AddSingleton<MoneyManagementViewModel>();

                // Logging provider
                services.AddLogging(builder =>
                {
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                // Registrar Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        base.OnStartup(e);

        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        var mainViewModel = _host.Services.GetRequiredService<MainViewModel>();

        mainWindow.DataContext = mainViewModel;
        mainWindow.Show();

        // Reset log file on startup
        var logFilePath = "log.txt";
        if (File.Exists(logFilePath))
        {
            File.Delete(logFilePath);
        }

        // Add file logger
        var loggerFactory = _host.Services.GetRequiredService<ILoggerFactory>();
        loggerFactory.AddProvider(new Excalibur.Infrastructure.FileLoggerProvider(logFilePath));
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // Salvar configurações antes de fechar se as checkboxes estiverem marcadas
        if (_host != null)
        {
            try
            {
                var mainViewModel = _host.Services.GetRequiredService<MainViewModel>();
                
                // Salvar configurações do Proposal se checkbox estiver marcada
                if (mainViewModel.Proposal?.SaveSettings == true)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var settingsService = _host.Services.GetRequiredService<Excalibur.Services.ISettingsService>();
                            var proposalSettings = new Excalibur.Models.ProposalSettings
                            {
                                StakeText = mainViewModel.Proposal.StakeText,
                                Duration = mainViewModel.Proposal.Duration,
                                DurationType = mainViewModel.Proposal.DurationType,
                                BarrierText = mainViewModel.Proposal.BarrierText,
                                Digit = mainViewModel.Proposal.Digit?.ToString() ?? "0",
                                IsContinuous = mainViewModel.Proposal.IsContinuous
                            };
                            await settingsService.SaveProposalSettingsAsync(proposalSettings);
                        }
                        catch { /* Ignorar erros de salvamento ao fechar */ }
                    });
                }
                
                // Salvar configurações do Money Management se checkbox estiver marcada
                if (mainViewModel.MoneyManagement?.SaveSettings == true)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var settingsService = _host.Services.GetRequiredService<Excalibur.Services.ISettingsService>();
                            var mmSettings = new Excalibur.Models.MoneyManagementSettings
                            {
                                FactorText = mainViewModel.MoneyManagement.FactorText,
                                LevelText = mainViewModel.MoneyManagement.LevelText,
                                MartingaleEnabled = mainViewModel.MoneyManagement.MartingaleEnabled,
                                AmostraText = mainViewModel.MoneyManagement.AmostraText,
                                WinText = mainViewModel.MoneyManagement.WinText,
                                LossText = mainViewModel.MoneyManagement.LossText
                            };
                            await settingsService.SaveMoneyManagementSettingsAsync(mmSettings);
                        }
                        catch { /* Ignorar erros de salvamento ao fechar */ }
                    });
                }
            }
            catch 
            { 
                /* Ignorar erros ao acessar ViewModels durante fechamento */ 
            }
        }
        
        _host?.Dispose();
        base.OnExit(e);
    }

    public App()
    {
        DispatcherUnhandledException += (s, args) =>
        {
            MessageBox.Show(args.Exception.Message, "Unhandled UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            args.Handled = true;
        };

        AppDomain.CurrentDomain.UnhandledException += (s, args) =>
        {
            if (args.ExceptionObject is Exception ex)
            {
                MessageBox.Show(ex.Message, "Unhandled Non-UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        };
    }
}