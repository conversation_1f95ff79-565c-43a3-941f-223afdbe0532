using Microsoft.Extensions.Logging;

namespace Excalibur.Services;

public interface IAppLoggerService
{
    void LogMartingale(string message, params object[] args);
    void LogTransaction(string message, params object[] args);
    void LogSimulation(string message, params object[] args);
    void LogSettings(string message, params object[] args);
    void LogError(Exception exception, string message, params object[] args);
    void LogWarning(string message, params object[] args);
    void LogInfo(string message, params object[] args);
}

public class AppLoggerService : IAppLoggerService
{
    private readonly ILogger<AppLoggerService> _logger;

    public AppLoggerService(ILogger<AppLoggerService> logger)
    {
        _logger = logger;
    }

    public void LogMartingale(string message, params object[] args)
    {
        _logger.LogDebug("[MARTINGALE] " + message, args);
    }

    public void LogTransaction(string message, params object[] args)
    {
        _logger.LogDebug("[TRANSACTION] " + message, args);
    }

    public void LogSimulation(string message, params object[] args)
    {
        _logger.LogDebug("[SIMULATION] " + message, args);
    }

    public void LogSettings(string message, params object[] args)
    {
        _logger.LogInformation("[SETTINGS] " + message, args);
    }

    public void LogError(Exception exception, string message, params object[] args)
    {
        _logger.LogError(exception, message, args);
    }

    public void LogWarning(string message, params object[] args)
    {
        _logger.LogWarning(message, args);
    }

    public void LogInfo(string message, params object[] args)
    {
        _logger.LogInformation(message, args);
    }
}