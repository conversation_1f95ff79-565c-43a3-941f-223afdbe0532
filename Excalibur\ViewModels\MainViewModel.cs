using Excalibur.Core.Interfaces;
using Excalibur.Models;
using Excalibur.Core.Models.DTOs.ApiResponses;
using Excalibur.Services;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace Excalibur.ViewModels;

public class MainViewModel : BaseViewModel, IDisposable
{
    private readonly IDerivApiService _derivApiService;
    private readonly ISettingsService _settingsService;
    private readonly IEventBusService _eventBus;
    private readonly ITimerManagerService _timerManager;
    private readonly ITransactionManagerService _transactionManager;
    private readonly IAppLoggerService _appLogger;
    private readonly SemaphoreSlim _timerSemaphore = new SemaphoreSlim(1, 1);
    private bool _disposed = false;
    private string? _profitTableTimerId;
    private decimal _lastTickPrice = 0;
    
    public AccountInfoViewModel AccountInfo { get; }
    public ActiveSymbolsViewModel Contracts { get; }
    public ProposalViewModel Proposal { get; }
    public MoneyManagementViewModel MoneyManagement { get; }
    public SimulationViewModel Simulation { get; }
    public PurchaseViewModel Purchase { get; }

    public MainViewModel(
        IDerivApiService derivApiService, 
        AccountInfoViewModel accountInfoViewModel, 
        ActiveSymbolsViewModel activeSymbolsViewModel, 
        ProposalViewModel proposalViewModel, 
        MoneyManagementViewModel moneyManagementViewModel,
        ISettingsService settingsService,
        IEventBusService eventBus,
        ITimerManagerService timerManager,
        ITransactionManagerService transactionManager,
        IAppLoggerService appLogger,
        IMartingaleService martingaleService)
    {
        _derivApiService = derivApiService;
        _settingsService = settingsService;
        _eventBus = eventBus;
        _timerManager = timerManager;
        _transactionManager = transactionManager;
        _appLogger = appLogger;
        
        // Inject dependencies into ViewModels
        AccountInfo = accountInfoViewModel;
        Contracts = activeSymbolsViewModel;
        Proposal = proposalViewModel;
        MoneyManagement = moneyManagementViewModel;
        
        // Create a separate martingale service instance for simulation
        var simulationMartingaleService = new MartingaleService();
        Simulation = new SimulationViewModel(simulationMartingaleService);
        
        Purchase = new PurchaseViewModel();
        
        // Contrato expirado -> buscar resultado real via API
        Purchase.ContractExpired += OnContractExpired;
        
        // Conectar eventos entre ViewModels
        Contracts.ContractSelected += OnContractSelected;
        
        // Conectar evento de compra do ProposalViewModel com o PurchaseViewModel
        Proposal.ContractPurchased += OnContractPurchased;
        
        // Conectar evento de atualização do ID do contrato
        Proposal.ContractIdUpdated += OnContractIdUpdated;
        
        // Conectar evento de simulação do ProposalViewModel com o SimulationViewModel
        _appLogger.LogInfo("Conectando evento ContractSimulated...");
        Proposal.ContractSimulated += OnContractSimulated;
        _appLogger.LogInfo("Evento ContractSimulated conectado com sucesso!");
        
        // Conectar evento de limpeza de tabelas
        Proposal.ClearTablesRequested += OnClearTablesRequested;
        
        // Conectar trigger para compra real quando simulação completar amostra
        Simulation.TriggerRealPurchase += OnTriggerRealPurchase;
        
        // Conectar evento para verificar transações ativas
        Proposal.CheckActiveSimulationTransactions += () => 
        {
            return Simulation.SimulationTransactions.Any(t => t.IsActive);
        };
        
        // Configurar simulação com valores iniciais do MoneyManagement
        _appLogger.LogInfo("Configurando simulação inicial: Amostra={Amostra}, Win={Win}, Loss={Loss}", 
            MoneyManagement.Amostra, MoneyManagement.Win, MoneyManagement.Loss);
        Simulation.UpdateSampleConfiguration(MoneyManagement.Amostra, MoneyManagement.Win, MoneyManagement.Loss);
        Simulation.SetMoneyManagement(MoneyManagement);
        Simulation.SetPurchaseViewModel(Purchase);
        Simulation.SetProposalViewModel(Proposal);
        
        // Configurar dependências do PurchaseViewModel
        Purchase.SetMoneyManagement(MoneyManagement);
        Purchase.SetProposalViewModel(Proposal);
        
        // Conectar evento de tick para atualizar transações em tempo real
        _derivApiService.TickReceived += OnTickReceived;

        // Receber resultado da ProfitTable
        _derivApiService.ProfitTableReceived += OnProfitTableReceived;
        
        // Receber detalhes de contratos individuais
        _derivApiService.OpenContractReceived += OnOpenContractReceived;
        
        // Timer para buscar profit_table periodicamente (a cada 15 segundos para reduzir rate limiting)
        _profitTableTimerId = _timerManager.CreateTimer(TimeSpan.FromSeconds(15), async (cancellationToken) =>
        {
            if (_disposed || cancellationToken.IsCancellationRequested) return;

            // Prevent overlapping executions
                if (!await _timerSemaphore.WaitAsync(100, cancellationToken))
                {
                    _appLogger.LogInfo("Timer execution skipped - previous execution still running");
                    return;
                }

                try
                {
                    if (_derivApiService?.IsConnected == true && !_disposed)
                    {
                        await _derivApiService.RequestProfitTableAsync();
                        
                        // Fazer uma cópia segura da coleção antes de iterar
                        var transactions = Purchase?.PurchaseTransactions?.ToList();
                        
                        if (transactions != null)
                        {
                            // Atualizar status de apenas contratos ativos para reduzir rate limiting
                            var activeTransactions = transactions.Where(tx => tx.IsActive && !string.IsNullOrEmpty(tx.RefId)).ToList();
                            
                            foreach (var tx in activeTransactions)
                            {
                                if (!_disposed)
                                {
                                    await _derivApiService.RequestContractDetailsAsync(tx.RefId);
                                    // Pequeno delay entre requests para evitar rate limiting
                                    await Task.Delay(200);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _appLogger.LogError(ex, "Error in timer execution");
                }
                finally
                {
                    _timerSemaphore.Release();
                }
        });

        // Solicitar profit_table logo na inicialização para atualizar contratos existentes
        if (_derivApiService.IsConnected)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await _derivApiService.RequestProfitTableAsync();
                }
                catch (Exception ex)
                {
                    _appLogger.LogError(ex, "Error requesting initial profit table");
                }
            });
        }

        // Carregar configurações salvas
        _ = Task.Run(LoadSettingsAsync);

        // Observar mudanças nas configurações de salvamento
        Proposal.PropertyChanged += OnProposalPropertyChanged;
        MoneyManagement.PropertyChanged += OnMoneyManagementPropertyChanged;
    }

    private async Task LoadSettingsAsync()
    {
        try
        {
            var settings = await _settingsService.LoadSettingsAsync();

            // Aplicar configurações do Proposal se salvamento estiver habilitado
            if (settings.SaveProposalSettings && settings.Proposal != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Proposal.StakeText = settings.Proposal.StakeText;
                    Proposal.Duration = settings.Proposal.Duration;
                    Proposal.DurationType = settings.Proposal.DurationType;
                    Proposal.BarrierText = settings.Proposal.BarrierText;
                    Proposal.Digit = int.TryParse(settings.Proposal.Digit, out int digit) ? digit : null;
                    Proposal.IsContinuous = settings.Proposal.IsContinuous;
                    Proposal.SaveSettings = true;

                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] Configurações do Proposal carregadas:");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - StakeText: {settings.Proposal.StakeText}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - Duration: {settings.Proposal.Duration}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - DurationType: {settings.Proposal.DurationType}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - IsContinuous: {settings.Proposal.IsContinuous}");
                });
            }

            // Aplicar configurações do MoneyManagement se salvamento estiver habilitado
            if (settings.SaveMoneyManagementSettings && settings.MoneyManagement != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MoneyManagement.FactorText = settings.MoneyManagement.FactorText;
                    MoneyManagement.LevelText = settings.MoneyManagement.LevelText;
                    MoneyManagement.MartingaleEnabled = settings.MoneyManagement.MartingaleEnabled;
                    MoneyManagement.AmostraText = settings.MoneyManagement.AmostraText;
                    MoneyManagement.WinText = settings.MoneyManagement.WinText;
                    MoneyManagement.LossText = settings.MoneyManagement.LossText;
                    MoneyManagement.SaveSettings = true;

                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] Configurações do MoneyManagement carregadas:");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - FactorText: {settings.MoneyManagement.FactorText}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - LevelText: {settings.MoneyManagement.LevelText}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - MartingaleEnabled: {settings.MoneyManagement.MartingaleEnabled}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - AmostraText: {settings.MoneyManagement.AmostraText}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - WinText: {settings.MoneyManagement.WinText}");
                    System.Diagnostics.Debug.WriteLine($"[SETTINGS] - LossText: {settings.MoneyManagement.LossText}");
                });
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SETTINGS] Erro ao carregar configurações: {ex.Message}");
        }
    }

    private void OnProposalPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ProposalViewModel.SaveSettings) && Proposal.SaveSettings)
        {
            _ = Task.Run(SaveProposalSettingsAsync);
        }
    }

    private void OnMoneyManagementPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(MoneyManagementViewModel.SaveSettings) && MoneyManagement.SaveSettings)
        {
            _ = Task.Run(SaveMoneyManagementSettingsAsync);
        }
    }

    private async Task SaveProposalSettingsAsync()
    {
        try
        {
            var settings = new ProposalSettings
            {
                StakeText = Proposal.StakeText,
                Duration = Proposal.Duration,
                DurationType = Proposal.DurationType,
                BarrierText = Proposal.BarrierText,
                Digit = Proposal.Digit?.ToString() ?? "0",
                IsContinuous = Proposal.IsContinuous
            };

            await _settingsService.SaveProposalSettingsAsync(settings);
            System.Diagnostics.Debug.WriteLine("[SETTINGS] Configurações do Proposal salvas");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SETTINGS] Erro ao salvar configurações do Proposal: {ex.Message}");
        }
    }

    private async Task SaveMoneyManagementSettingsAsync()
    {
        try
        {
            var settings = new MoneyManagementSettings
            {
                FactorText = MoneyManagement.FactorText,
                LevelText = MoneyManagement.LevelText,
                MartingaleEnabled = MoneyManagement.MartingaleEnabled,
                AmostraText = MoneyManagement.AmostraText,
                WinText = MoneyManagement.WinText,
                LossText = MoneyManagement.LossText
            };

            await _settingsService.SaveMoneyManagementSettingsAsync(settings);
            System.Diagnostics.Debug.WriteLine("[SETTINGS] Configurações do MoneyManagement salvas");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SETTINGS] Erro ao salvar configurações do MoneyManagement: {ex.Message}");
        }
    }

    private void OnContractSelected(object? sender, (string Symbol, Excalibur.Core.Models.DTOs.ApiResponses.ContractType Contract) e)
    {
        Proposal.SelectedSymbol = e.Symbol;
        Proposal.SelectedContract = e.Contract;
    }
    
    private void OnContractPurchased(object? sender, ContractPurchasedEventArgs e)
    {
        if (_disposed) return;
        
        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ EVENTO OnContractPurchased RECEBIDO!");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractType: {e.ContractType}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractId: {e.ContractId}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] StartSpot: {e.StartSpot}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Stake: {e.Stake}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Payout: {e.Payout}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Duration: {e.Duration}{e.DurationType}");

            // ⚡ NOVA LÓGICA: Verificar se há contratos finalizados que precisam processar martingale
            CheckAndProcessPendingMartingale();
            
            var transaction = new ContractTransaction
            {
                Type = e.ContractType,
                RefId = e.ContractId,
                BuyTime = DateTime.Now,
                StartSpot = e.StartSpot,
                EndSpot = e.StartSpot, // Inicialmente igual ao StartSpot
                Stake = e.Stake,
                Payout = e.Payout,
                TotalProfitLoss = 0, // Inicialmente zero
                IsActive = true,
                Duration = e.Duration,
                DurationType = e.DurationType
            };
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] Criando transação: RefId={transaction.RefId}, BuyTime={transaction.BuyTime:HH:mm:ss}");
            
            Purchase.AddTransaction(transaction);

            // Registrar transação no sistema de martingale
            _transactionManager.RegisterPendingTransaction(transaction.RefId);

            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Transação adicionada à tabela de compras!");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Total de transações na tabela: {Purchase.PurchaseTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Transação registrada: {transaction.RefId}, aguardando resultado...");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Erro em OnContractPurchased: {ex.Message}");
        }
    }
    
    private void OnContractIdUpdated(object? sender, ContractIdUpdatedEventArgs e)
    {
        // Atualizar o ID do contrato na transação
        Purchase.UpdateContractId(e.OldProposalId, e.NewContractId);
    }
    
    private void OnTickReceived(object? sender, TickData tickData)
    {
        // Validar dados do tick antes de processar
        if (tickData == null || tickData.Price <= 0 || tickData.Price > 1000000) // Validação básica para preços absurdos
        {
            System.Diagnostics.Debug.WriteLine($"[TICK] ⚠️ Tick inválido ignorado: Price={(tickData != null ? tickData.Price : 0)}");
            return;
        }
        
        // CRÍTICO: Verificar se o tick é do símbolo selecionado
        if (tickData.Symbol != Proposal.SelectedSymbol)
        {
            System.Diagnostics.Debug.WriteLine($"[TICK] ⚠️ Tick de símbolo diferente ignorado: {tickData.Symbol} (selecionado: {Proposal.SelectedSymbol})");
            return;
        }
        
        // Validar se o preço não mudou drasticamente (mais de 50% em um tick)
        if (_lastTickPrice > 0)
        {
            var priceChange = Math.Abs(tickData.Price - _lastTickPrice) / _lastTickPrice;
            if (priceChange > 0.5m) // Mudança maior que 50%
            {
                System.Diagnostics.Debug.WriteLine($"[TICK] ⚠️ Mudança de preço suspeita ignorada: {_lastTickPrice:F3} → {tickData.Price:F3} ({priceChange:P1})");
                return;
            }
        }
        
        // Armazenar último tick recebido para uso como fallback
        _lastTickPrice = tickData.Price;
        
        System.Diagnostics.Debug.WriteLine($"[TICK] ✅ Tick válido para {tickData.Symbol}: {tickData.Price:F3}");
        
        // Atualizar spot atual para cálculo do P/L em tempo real
        Purchase.UpdateCurrentSpot(tickData.Price);
        Simulation.UpdateCurrentSpot(tickData.Price);
    }

    private void OnContractExpired(object? sender, ContractTransaction transaction)
{
    if (_disposed || transaction == null) return;

    var isWinning = transaction.IsCurrentlyWinning(transaction.EndSpot);
    
    _appLogger.LogInfo("[CONTRACT-EXPIRED] ===== CONTRATO EXPIRADO =====");
    _appLogger.LogInfo("[CONTRACT-EXPIRED] RefId: {RefId}", transaction.RefId);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] Tipo: {Type}", transaction.Type);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] StartSpot: {StartSpot:F3}", transaction.StartSpot);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] EndSpot: {EndSpot:F3}", transaction.EndSpot);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] Stake: {Stake:F2}", transaction.Stake);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] P/L: {PL:F2}", transaction.TotalProfitLoss);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] Status: {Status}", isWinning ? "VITÓRIA" : "PERDA");
    _appLogger.LogInfo("[CONTRACT-EXPIRED] IsWaitingForResult: {Waiting}", _transactionManager.IsWaitingForResult);
    _appLogger.LogInfo("[CONTRACT-EXPIRED] PendingTransactionId: {PendingId}", _transactionManager.PendingTransactionId ?? "NULL");

    // Processar martingale se esta transação estava sendo aguardada
    if (_transactionManager.IsWaitingForResult && _transactionManager.PendingTransactionId == transaction.RefId)
    {
        _appLogger.LogInfo("[CONTRACT-EXPIRED] ✅ Processando martingale para transação aguardada");
        ProcessMartingaleLogic(transaction, isWinning);  // ✅ APENAS ESTE processamento de martingale
    }
    else
    {
        _appLogger.LogInfo("[CONTRACT-EXPIRED] ⚠️ Transação não estava sendo aguardada - martingale não processado");
    }
    
    // ❌ LINHA REMOVIDA para evitar processamento duplicado de martingale:
    // Purchase.ProcessTransactionResult(transaction);
    
    _appLogger.LogInfo("[CONTRACT-EXPIRED] ✅ Processamento de martingale centralizado concluído");

    // Solicitar a profit_table geral para atualizar todos os contratos
    _ = Task.Run(async () =>
    {
        try
        {
            if (_derivApiService?.IsConnected == true && !_disposed)
            {
                await _derivApiService.RequestProfitTableAsync();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error handling contract expiry: {ex.Message}");
        }
    });
}

    private void ProcessMartingaleLogic(ContractTransaction expiredTransaction, bool isWinning)
{
    try
    {
        _appLogger.LogInfo("[MARTINGALE] ===== PROCESSANDO RESULTADO =====");
        _appLogger.LogInfo("[MARTINGALE] RefId: {RefId}", expiredTransaction.RefId);
        _appLogger.LogInfo("[MARTINGALE] Tipo: {Type}", expiredTransaction.Type);
        _appLogger.LogInfo("[MARTINGALE] StartSpot: {StartSpot:F3}", expiredTransaction.StartSpot);
        _appLogger.LogInfo("[MARTINGALE] EndSpot: {EndSpot:F3}", expiredTransaction.EndSpot);
        _appLogger.LogInfo("[MARTINGALE] Stake: {Stake:F2}", expiredTransaction.Stake);
        _appLogger.LogInfo("[MARTINGALE] P/L: {PL:F2}", expiredTransaction.TotalProfitLoss);
        _appLogger.LogInfo("[MARTINGALE] Resultado detectado: {Result}", isWinning ? "VITÓRIA" : "PERDA");
        _appLogger.LogInfo("[MARTINGALE] Stake atual no Proposal: {CurrentStake}", Proposal?.StakeText ?? "NULL");

        // Limpar estado de espera
        _transactionManager.CompletePendingTransaction();

        // Se não há ProposalViewModel, martingale está desabilitado, ou simulação foi parada, não fazer nada
        if (Proposal == null || !MoneyManagement.MartingaleEnabled || !Proposal.IsAutoSimulating)
        {
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Martingale desabilitado, ProposalViewModel nulo, ou simulação parada");
            return;
        }

        // ✅ CAPTURAR STAKE BASE SE AINDA NÃO FOI CAPTURADA
        if (MoneyManagement.BaseStake <= 0.35m) // Valor padrão = ainda não foi capturada
        {
            // Pegar a stake base do início da sequência ou da configuração atual
            var currentBaseStake = GetOriginalStakeFromSequence() ?? Proposal?.Stake ?? 0.35m;
            MoneyManagement.SetBaseStake(currentBaseStake);
            _appLogger.LogInfo("[MARTINGALE] Stake base capturada: {BaseStake:F2}", currentBaseStake);
        }

        var isLoss = !isWinning;
        
        _appLogger.LogInfo("[MARTINGALE] Estado antes do cálculo:");
        _appLogger.LogInfo("[MARTINGALE] - IsInMartingaleSequence: {InSequence}", MoneyManagement.IsInMartingaleSequence);
        _appLogger.LogInfo("[MARTINGALE] - CurrentMartingaleLevel: {Level}", MoneyManagement.CurrentMartingaleLevel);
        _appLogger.LogInfo("[MARTINGALE] - BaseStake: {BaseStake:F2}", MoneyManagement.BaseStake);  // ✅ Agora mostra BaseStake
        _appLogger.LogInfo("[MARTINGALE] - IsLoss: {IsLoss}", isLoss);
        
        // ✅ USAR STAKE BASE AO INVÉS DE PROPOSAL.STAKE
        var (nextStake, shouldContinueSequence) = MoneyManagement.GetNextStake(MoneyManagement.BaseStake, isLoss);
        
        _appLogger.LogInfo("[MARTINGALE] Estado após o cálculo:");
        _appLogger.LogInfo("[MARTINGALE] - NextStake: {NextStake:F2}", nextStake);
        _appLogger.LogInfo("[MARTINGALE] - ShouldContinueSequence: {ShouldContinue}", shouldContinueSequence);
        _appLogger.LogInfo("[MARTINGALE] - IsInMartingaleSequence: {InSequence}", MoneyManagement.IsInMartingaleSequence);
        _appLogger.LogInfo("[MARTINGALE] - CurrentMartingaleLevel: {Level}", MoneyManagement.CurrentMartingaleLevel);

        // Determinar se deve executar próxima compra
        bool shouldExecuteNextPurchase = false;
        
        if (isLoss && MoneyManagement.MartingaleEnabled && shouldContinueSequence)
        {
            // Em caso de perda com martingale ativo e ainda dentro do limite de níveis
            shouldExecuteNextPurchase = true;
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Perda com martingale ativo - executando próxima compra (nível válido)");
        }
        else if (isLoss && MoneyManagement.MartingaleEnabled && !shouldContinueSequence)
        {
            // Em caso de perda mas atingiu nível máximo
            shouldExecuteNextPurchase = false;
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Perda mas nível máximo atingido - sequência finalizada");
        }
        else if (isWinning)
        {
            // Em caso de vitória, resetar a stake para o valor original e não executar compra automática
            shouldExecuteNextPurchase = false;
            
            _appLogger.LogInfo("[MARTINGALE] ✅ VITÓRIA DETECTADA - Resetando stake");
            _appLogger.LogInfo("[MARTINGALE] Stake antes do reset: {StakeBefore}", Proposal != null ? Proposal.StakeText : "NULL");
            _appLogger.LogInfo("[MARTINGALE] Stake após reset (calculada): {StakeAfter:F2}", nextStake);
            
            // CRÍTICO: Usar Dispatcher para garantir que a atualização aconteça no thread da UI
            Application.Current.Dispatcher.Invoke(() =>
            {
                var oldStake = Proposal != null ? Proposal.StakeText : string.Empty;
                if (Proposal != null)
                {
                    Proposal.StakeText = nextStake.ToString("F2");
                    _appLogger.LogInfo("[MARTINGALE] ✅ Stake atualizada na UI: {OldStake} → {NewStake}", oldStake ?? string.Empty, Proposal.StakeText ?? string.Empty);
                }
            });
            
            _appLogger.LogInfo("[MARTINGALE] Vitória - sequência de martingale finalizada");
        }
        else
        {
            // Martingale desabilitado
            shouldExecuteNextPurchase = false;
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Martingale desabilitado - não executando compra automática");
        }

        if (shouldExecuteNextPurchase)
        {
            // Verificar se já há uma transação pendente
            if (!_transactionManager.CanExecuteNewTransaction())
            {
                _appLogger.LogInfo("Já existe transação pendente ({PendingId}), aguardando...", _transactionManager.PendingTransactionId ?? "NULL");
                return;
            }

            // Atualizar stake no ProposalViewModel
            if (Proposal != null)
            {
                Proposal.StakeText = nextStake.ToString("F2");
            }
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Stake atualizada para: {nextStake}");

            // Aguardar um pouco antes de executar nova compra para evitar rate limiting
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(3000); // Aguardar 3 segundos para maior estabilidade

                    if (_disposed || Proposal == null) return;

                    // Verificar novamente se não há transação pendente antes de executar
                    if (!_transactionManager.CanExecuteNewTransaction())
                    {
                        _appLogger.LogInfo("Transação pendente detectada durante execução, cancelando...");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Executando nova compra com stake: {nextStake}");

                    // Executar nova proposta e compra
                    await Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        if (Proposal?.CalculateProposalCommand?.CanExecute(null) == true)
                        {
                            await Task.Run(() => Proposal.CalculateProposalCommand.Execute(null));

                            // Aguardar proposta ser calculada
                            await Task.Delay(2000);

                            // Verificar uma última vez antes da compra
                            if (!_transactionManager.CanExecuteNewTransaction())
                            {
                                _appLogger.LogInfo("Transação pendente detectada antes da compra, cancelando...");
                                return;
                            }

                            // Executar compra
                            if (Proposal?.BuyCommand?.CanExecute(null) == true)
                            {
                                Proposal.BuyCommand.Execute(null);
                                System.Diagnostics.Debug.WriteLine($"[MARTINGALE] ✅ Nova compra executada automaticamente!");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[MARTINGALE] ❌ BuyCommand não disponível");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] ❌ CalculateProposalCommand não disponível");
                        }
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE] ❌ Erro ao executar nova compra: {ex.Message}");
                }
            });
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE] Não executando próxima compra automática");
        }
        
        // CORREÇÃO: Sempre processar fila de compras pendentes após martingale
        // A fila pode ter transações aguardando independentemente do estado do martingale
        ProcessPendingPurchasesFromWins();
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"[MARTINGALE] ❌ Erro ao processar lógica de martingale: {ex.Message}");
    }
}

// ✅ MÉTODO AUXILIAR PARA ADICIONAR
private decimal? GetOriginalStakeFromSequence()
{
    try
    {
        // Pegar a primeira transação da sequência atual para determinar a stake base
        var firstTransaction = Purchase?.PurchaseTransactions?
            .Where(t => !t.IsActive || t.BuyTime > DateTime.Now.AddMinutes(-5)) // Transações recentes
            .OrderBy(t => t.BuyTime)
            .FirstOrDefault();

        if (firstTransaction != null)
        {
            _appLogger.LogInfo("[MARTINGALE] Stake base detectada da primeira transação: {Stake:F2}", firstTransaction.Stake);
            return firstTransaction.Stake;
        }
    }
    catch (Exception ex)
    {
        _appLogger.LogInfo("[MARTINGALE] Erro ao detectar stake base: {Error}", ex.Message);
    }

    return null;
}// NOVA LÓGICA: Verificar e processar sequência de martingale
    private void CheckAndProcessPendingMartingale()
    {
        try
        {
            _appLogger.LogInfo("[MARTINGALE-CHECK] ⚡ Verificando sequência de martingale...");
            
            if (!MoneyManagement.MartingaleEnabled)
            {
                var baseStake = MoneyManagement.BaseStake > 0.35m ? MoneyManagement.BaseStake : 0.35m;
                _appLogger.LogInfo("[MARTINGALE-CHECK] Martingale desabilitado - usando stake base: {BaseStake:F2}", baseStake);
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (Proposal != null) Proposal.StakeText = baseStake.ToString("F2");
                });
                return;
            }
            
            // Pegar todos os contratos ordenados por tempo de compra (mais recente primeiro)
            var allContracts = Purchase?.PurchaseTransactions?
                .OrderByDescending(t => t.BuyTime)
                .ToList();

            if (allContracts == null || !allContracts.Any())
            {
                System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Nenhum contrato encontrado - resetando martingale");
                MoneyManagement.ResetMartingale();
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (Proposal != null)
                    {
                        var baseStake = MoneyManagement.BaseStake > 0.35m ? MoneyManagement.BaseStake : 0.35m;
                        Proposal.StakeText = baseStake.ToString("F2");
                        _appLogger.LogInfo("[MARTINGALE-CHECK] Stake resetada para valor base: {BaseStake:F2}", baseStake);
                    }
                });
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Analisando sequência de {allContracts.Count} contratos...");

            // Reconstruir a sequência de martingale desde o início
            MoneyManagement.ResetMartingale();
            decimal baseStakeValue = MoneyManagement.BaseStake > 0.35m ? MoneyManagement.BaseStake : 0.35m;
            decimal currentStake = baseStakeValue; // Stake base
            
            // Processar contratos do mais antigo para o mais recente para reconstruir a sequência
            var contractsInOrder = allContracts.OrderBy(t => t.BuyTime).ToList();
            
            foreach (var contract in contractsInOrder)
            {
                System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Processando contrato: RefId={contract.RefId}, " +
                    $"BuyTime={contract.BuyTime:HH:mm:ss}, IsActive={contract.IsActive}");

                // Determinar resultado do contrato
                bool isWinning;
                if (contract.IsActive)
                {
                    // Contrato ainda ativo - verificar status atual
                    isWinning = contract.IsCurrentlyWinning(contract.EndSpot);
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Contrato ativo - Status: {(isWinning ? "GANHANDO" : "PERDENDO")}");
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] StartSpot={contract.StartSpot:F3}, EndSpot={contract.EndSpot:F3}");
                }
                else
                {
                    // Contrato finalizado - usar resultado final
                    isWinning = contract.TotalProfitLoss > 0;
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Contrato finalizado - Resultado: {(isWinning ? "VITÓRIA" : "PERDA")}, P/L={contract.TotalProfitLoss:F2}");
                }

                // Aplicar lógica de martingale
                bool isLoss = !isWinning;
                var (nextStake, shouldContinue) = MoneyManagement.GetNextStake(baseStakeValue, isLoss);
                
                System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Resultado: {(isWinning ? "VITÓRIA" : "PERDA")}, " +
                    $"Próxima stake: {nextStake}, Nível: {MoneyManagement.CurrentMartingaleLevel}");
                
                currentStake = nextStake;
            }
            
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] ✅ Sequência reconstruída - Próxima stake: {currentStake}");
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] Nível atual do martingale: {MoneyManagement.CurrentMartingaleLevel}");
            
            // Atualizar stake para a próxima compra
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (Proposal != null)
                {
                    var oldStake = Proposal.StakeText;
                    Proposal.StakeText = currentStake.ToString("F2");
                    System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] ✅ Stake atualizada: {oldStake} → {Proposal.StakeText}");
                }
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-CHECK] ❌ Erro ao verificar martingale: {ex.Message}");
            // Em caso de erro, usar stake base
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (Proposal != null)
                {
                    var baseStake = MoneyManagement.BaseStake > 0.35m ? MoneyManagement.BaseStake : 0.35m;
                    Proposal.StakeText = baseStake.ToString("F2");
                }
            });
        }
    }

    private void ProcessPendingPurchasesFromWins()
    {
        try
        {
            if (!_transactionManager.HasQueuedTransactions())
            {
                return;
            }

            _appLogger.LogInfo("Processando fila de compras pendentes");

            // Processar apenas uma compra da fila por vez
            var pendingTime = _transactionManager.GetNextQueuedTransaction();
            if (pendingTime.HasValue)
            {
                _appLogger.LogInfo("Executando compra pendente da fila (criada em: {Time})", pendingTime.Value.ToString("HH:mm:ss"));

                // Executar a compra pendente
                ExecuteRealPurchase();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Erro ao processar fila de compras pendentes: {ex.Message}");
        }
    }

    private void ExecuteRealPurchase()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ Executando compra real...");

            // Log de entrada no método
            try
            {
                string logFile = "simulation_debug.log";
                string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - EXECUTE REAL PURCHASE: Started\n";
                System.IO.File.AppendAllText(logFile, logEntry);
            }
            catch { /* Ignorar erros de arquivo */ }

            var proposalVM = Proposal as ProposalViewModel;
            bool shouldContinueSimulation = proposalVM?.IsContinuous == true;
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ProposalVM disponível: {proposalVM != null}, IsContinuous: {shouldContinueSimulation}");

            // Se toggle "Manter" não estiver ativado, parar simulação antes da compra real
            if (proposalVM != null && proposalVM.IsAutoSimulating && !shouldContinueSimulation)
            {
                System.Diagnostics.Debug.WriteLine($"[MAIN] 🛑 Parando simulação automática antes da compra real (Manter=OFF)");
                _ = Task.Run(() => proposalVM.SimulateCommand.Execute(null)); // Parar simulação
            }
            else if (shouldContinueSimulation)
            {
                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Mantendo simulação automática ativa (Manter=ON)");
            }

            // Executar compra real usando o comando do ProposalViewModel
            _ = Task.Run(async () =>
            {
                try
                {
                    // Se parou simulação, aguardar um pouco mais para garantir estabilidade
                    if (!shouldContinueSimulation)
                    {
                        await Task.Delay(1000);
                    }
                    
                    // Garantir que há proposta válida antes de tentar comprar
                    System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Forçando nova proposta antes da compra real...");
                    
                    // Sempre solicitar nova proposta antes da compra real
                    if (Proposal?.CalculateProposalCommand?.CanExecute(null) == true)
                    {
                        await Task.Run(() => Proposal.CalculateProposalCommand.Execute(null));
                        
                        // Aguardar proposta ser calculada
                        await Task.Delay(2000);
                        
                        // Aguardar até que a proposta esteja pronta (máximo 10 segundos)
                        int attempts = 0;
                        const int maxAttempts = 20;
                        
                        while (attempts < maxAttempts)
                        {
                            var canExecute = Proposal?.BuyCommand?.CanExecute(null) == true;
                            var hasPayout = (Proposal?.Payout ?? 0) > 0;
                            var isNotLoading = Proposal?.IsLoading != true;
                            
                            System.Diagnostics.Debug.WriteLine($"[MAIN] 🔍 Tentativa {attempts + 1}/{maxAttempts} - CanExecute: {canExecute}, Payout: {Proposal?.Payout ?? 0}, Loading: {!isNotLoading}");
                            
                            if (canExecute && hasPayout && isNotLoading)
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] 🚀 Executando comando BuyCommand...");
                                
                                // Executar no thread principal da UI
                                await Application.Current.Dispatcher.InvokeAsync(() =>
                                {
                                    if (Proposal?.BuyCommand?.CanExecute(null) == true)
                                    {
                                        Proposal.BuyCommand.Execute(null);
                                        System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Compra real executada automaticamente! Simulação contínua: {shouldContinueSimulation}");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ BuyCommand não pode ser executado no momento da compra");
                                    }
                                });
                                
                                return;
                            }
                            
                            attempts++;
                            await Task.Delay(500);
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Timeout: Não foi possível executar compra real após {maxAttempts} tentativas");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Não foi possível solicitar nova proposta - CalculateProposalCommand não disponível");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Error executing real purchase: {ex.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Error in ExecuteRealPurchase: {ex.Message}");
        }
    }

    private void OnContractSimulated(object? sender, ContractSimulatedEventArgs e)
    {
        if (_disposed || e == null) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ======= EVENTO CONTRACTSIMULATED RECEBIDO =======");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Sender: {sender?.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractType: {e.ContractType}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractId: {e.ContractId}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] StartSpot recebido: {e.StartSpot}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Proposal.CurrentSpot: {Proposal?.CurrentSpot ?? 0}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Stake: {e.Stake}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Payout: {e.Payout}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Duration: {e.Duration} {e.DurationType}");

            // Atualizar configuração da simulação com valores atuais do MoneyManagement
            System.Diagnostics.Debug.WriteLine($"[MAIN] Atualizando configuração: Amostra={MoneyManagement.Amostra}, Win={MoneyManagement.Win}, Loss={MoneyManagement.Loss}");
            Simulation.UpdateSampleConfiguration(MoneyManagement.Amostra, MoneyManagement.Win, MoneyManagement.Loss);

            // Criar transação simulada usando a stake real do ProposalViewModel
            var actualStake = Proposal?.Stake ?? e.Stake;
            
            // Usar o tempo atual como BuyTime para timing preciso
            var simulationBuyTime = DateTime.Now;
            
            // Determinar o StartSpot a ser usado (com múltiplos fallbacks)
            var startSpotToUse = e.StartSpot > 0 ? e.StartSpot : 
                                 (Proposal?.CurrentSpot > 0 ? Proposal.CurrentSpot : _lastTickPrice);
            
            var simulatedTransaction = new ContractTransaction
            {
                Type = e.ContractType,
                RefId = e.ContractId,
                BuyTime = simulationBuyTime, // Usar tempo atual para timing preciso
                StartSpot = startSpotToUse, // Usar spot atual se e.StartSpot for 0
                EndSpot = Proposal?.CurrentSpot ?? e.StartSpot, // Usar spot atual do ProposalViewModel
                Stake = actualStake, // Usar stake real do painel
                Payout = e.Payout,
                TotalProfitLoss = 0,
                IsActive = true,
                Duration = e.Duration,
                DurationType = e.DurationType
            };

            System.Diagnostics.Debug.WriteLine($"[MAIN] StartSpot usado na transação: {startSpotToUse} (original: {e.StartSpot}, Proposal.CurrentSpot: {Proposal?.CurrentSpot ?? 0}, lastTick: {_lastTickPrice})");

            System.Diagnostics.Debug.WriteLine($"[MAIN] Usando stake: {actualStake} (ProposalVM: {Proposal?.Stake}, EventArgs: {e.Stake})");
            System.Diagnostics.Debug.WriteLine($"[MAIN] BuyTime da simulação: {simulationBuyTime:HH:mm:ss.fff}");

            // Verificar se não excedeu o limite de transações simuladas
            if (Simulation.IsAtTransactionLimit)
            {
                System.Diagnostics.Debug.WriteLine($"[MAIN] Limite de transações simuladas atingido. Parando simulação.");
                if (Proposal != null && Proposal.IsAutoSimulating)
                {
                    // Parar a simulação automática
                    Task.Run(() => Proposal.SimulateCommand.Execute(null));
                }
                return;
            }
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] Chamando Simulation.AddTransaction...");
            Simulation.AddTransaction(simulatedTransaction);
            System.Diagnostics.Debug.WriteLine($"[MAIN] Transação simulada adicionada à tabela Simulação - Total: {Simulation.SimulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ======= EVENTO CONTRACTSIMULATED PROCESSADO COM SUCESSO =======");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error handling contract simulation: {ex.Message}");
        }
    }

    private void OnClearTablesRequested(object? sender, EventArgs e)
    {
        if (_disposed) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] 🧹 Limpando tabelas Simulação e Compras conforme solicitado");
            
            // Limpar tabela de simulação
            Simulation?.ClearTransactions();
            
            // Limpar tabela de compras
            Purchase?.ClearTransactions();
            
            // Limpar fila de compras pendentes
            _transactionManager.ClearQueue();
            _appLogger.LogInfo("Fila de compras pendentes limpa");
            
            // Resetar martingale para valor inicial
            MoneyManagement?.ResetMartingale();
            _appLogger.LogInfo("Martingale resetado para valor inicial");
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Tabelas limpas e martingale resetado com sucesso");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error clearing tables: {ex.Message}");
        }
    }

    private void OnTriggerRealPurchase(object? sender, EventArgs e)
    {
        if (_disposed) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ TRIGGER PARA COMPRA REAL RECEBIDO! Sender: {sender?.GetType().Name}");

            // Log detalhado para debug
            try
            {
                string logFile = "simulation_debug.log";
                string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - MAIN TRIGGER: CanExecute={_transactionManager.CanExecuteNewTransaction()}, IsWaiting={_transactionManager.IsWaitingForResult}, PendingId={_transactionManager.PendingTransactionId ?? "NULL"}\n";
                System.IO.File.AppendAllText(logFile, logEntry);
            }
            catch { /* Ignorar erros de arquivo */ }

            // Verificar se deve aguardar transação anterior (controle sequencial)
            if (!_transactionManager.CanExecuteNewTransaction())
            {
                _appLogger.LogInfo("Aguardando resultado da transação anterior ({PendingId}), ignorando trigger", _transactionManager.PendingTransactionId ?? "NULL");
                
                // CORREÇÃO: Adicionar à fila em vez de ignorar
                _transactionManager.QueueTransaction(DateTime.Now);
                _appLogger.LogInfo("Compra adicionada à fila devido a transação pendente");
                
                // Log para arquivo
                try
                {
                    string logFile = "simulation_debug.log";
                    string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - MAIN TRIGGER: QUEUED devido a transação pendente\n";
                    System.IO.File.AppendAllText(logFile, logEntry);
                }
                catch { /* Ignorar erros de arquivo */ }
                return;
            }

            // CORREÇÃO: Verificar e aplicar martingale nas compras vindas da simulação
            System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Verificando estado do martingale antes da compra...");
            CheckAndProcessPendingMartingale();

            // Verificar se há martingale ativo na tabela Compras
            bool isMartingaleActive = MoneyManagement.IsInMartingaleSequence;
            if (isMartingaleActive)
            {
                _appLogger.LogInfo("Martingale está ativo na tabela Compras (nível {Level}), adicionando à fila", MoneyManagement.CurrentMartingaleLevel);
                
                // Adicionar à fila de compras pendentes
                _transactionManager.QueueTransaction(DateTime.Now);
                _appLogger.LogInfo("Compra adicionada à fila. Total na fila: {Count}", _transactionManager.HasQueuedTransactions() ? "sim" : "não");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Nenhum martingale ativo - pode executar compra real");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Stake atual: {Proposal?.StakeText ?? "NULL"}");

            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ TRIGGER PARA COMPRA REAL ATIVADO! Executando compra automaticamente...");

            // Log antes de executar compra real
            try
            {
                string logFile = "simulation_debug.log";
                string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - MAIN TRIGGER: EXECUTING REAL PURCHASE\n";
                System.IO.File.AppendAllText(logFile, logEntry);
            }
            catch { /* Ignorar erros de arquivo */ }

            // Atualizar stake do Proposal com a stake calculada pela simulação
            if (Simulation != null && Proposal != null)
            {
                var realPurchaseStake = Simulation.RealPurchaseStake;
                var oldStake = Proposal.StakeText;
                Proposal.StakeText = realPurchaseStake.ToString("F2");

                try
                {
                    string logFile = "simulation_debug.log";
                    string logEntry = $"{DateTime.Now:HH:mm:ss.fff} - STAKE UPDATE: {oldStake} → {Proposal.StakeText} (Level={Simulation.RealPurchaseLevel})\n";
                    System.IO.File.AppendAllText(logFile, logEntry);
                }
                catch { /* Ignorar erros de arquivo */ }
            }

            // Executar compra real imediatamente
            ExecuteRealPurchase();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Error triggering real purchase: {ex.Message}");
        }
    }

    private void OnProfitTableReceived(object? sender, Excalibur.Core.Models.DTOs.ApiResponses.ProfitTableTransaction tx)
    {
        if (_disposed || tx == null) return;

        try
        {
            // Log para diagnóstico - adicionando mais detalhes
            System.Diagnostics.Debug.WriteLine($"PROFIT_TABLE: ContractID={tx.ContractIdString}, TransactionID={tx.TransactionIdString}, PurchaseTime={tx.PurchaseTime}, Profit={tx.Profit}");

            // Converter timestamp para DateTime para comparação
            var txDateTime = DateTimeOffset.FromUnixTimeSeconds(tx.PurchaseTime).DateTime;
            
            // Verificar se o contrato é recente (nas últimas 24 horas)
            var isRecent = (DateTime.UtcNow - txDateTime).TotalHours < 24;
            
            // Para transações recentes, imprimir todos os detalhes
            if (isRecent)
            {
                System.Diagnostics.Debug.WriteLine($"PROFIT_TABLE RECENTE: TX DateTime={txDateTime}, Profit={tx.Profit}");
                
                // Mostrar todos os contratos disponíveis para comparação - thread safe copy
                var transactions = Purchase?.PurchaseTransactions?.ToList();
                if (transactions != null)
                {
                    foreach (var contract in transactions)
                    {
                        System.Diagnostics.Debug.WriteLine($"CONTRATO LOCAL: RefId={contract.RefId}, BuyTime={contract.BuyTime.ToUniversalTime()}, Profit={contract.TotalProfitLoss}");
                    }
                }
            }

            // 1. Tentar correspondência direta por ID
            if (!string.IsNullOrEmpty(tx.TransactionIdString))
            {
                Purchase?.UpdateProfitFromApi(tx.TransactionIdString, tx.Profit);
            }

            if (!string.IsNullOrEmpty(tx.ContractIdString) && tx.ContractIdString != tx.TransactionIdString)
            {
                Purchase?.UpdateProfitFromApi(tx.ContractIdString, tx.Profit);
            }
            
            // 2. Tentar correspondência por horário (quando o ID não funciona)
            // Isso ajuda quando IDs ficam dessincronizados
            if (isRecent)
            {
                Purchase?.UpdateProfitFromApiByTime(txDateTime, tx.Profit, 30);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error processing profit table: {ex.Message}");
        }
    }

    private void OnOpenContractReceived(object? sender, Excalibur.Core.Models.DTOs.ApiResponses.OpenContractDetails contract)
    {
        if (_disposed || contract == null) return;

        try
        {
            // Atualizar usando tanto contractId quanto transactionId para garantir que encontrará
            var refId = !string.IsNullOrEmpty(contract.TransactionIdString) ? contract.TransactionIdString : contract.ContractIdString;
            
            System.Diagnostics.Debug.WriteLine($"CONTRACT DETAILS: ContractID={contract.ContractIdString}, TransactionID={contract.TransactionIdString}, P/L={contract.Profit}, Status={contract.Status}");
            
            // Aplicar o lucro/prejuízo oficial, independentemente do status do contrato
            // Isso garante que usaremos sempre o valor oficial, mesmo se ainda estiver aberto
            if (!string.IsNullOrEmpty(refId))
            {
                Purchase?.UpdateProfitFromApi(refId, contract.Profit);
            }
            
            // Se não encontrou pelo ID principal, tentar o ID alternativo
            if (!string.IsNullOrEmpty(contract.TransactionIdString) && 
                !string.IsNullOrEmpty(contract.ContractIdString) && 
                contract.TransactionIdString != contract.ContractIdString)
            {
                Purchase?.UpdateProfitFromApi(contract.ContractIdString, contract.Profit);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error processing contract details: {ex.Message}");
        }
    }
    
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            // Stop and dispose timer first to prevent new executions
            if (_profitTableTimerId != null)
            {
                _timerManager.RemoveTimer(_profitTableTimerId);
            }

            // Unsubscribe from events to prevent memory leaks
            if (Purchase != null)
            {
                Purchase.ContractExpired -= OnContractExpired;
            }

            if (Contracts != null)
            {
                Contracts.ContractSelected -= OnContractSelected;
            }

            if (Proposal != null)
            {
                Proposal.ContractPurchased -= OnContractPurchased;
                Proposal.ContractIdUpdated -= OnContractIdUpdated;
                Proposal.ContractSimulated -= OnContractSimulated;
                Proposal.ClearTablesRequested -= OnClearTablesRequested;
                Proposal.PropertyChanged -= OnProposalPropertyChanged;
            }

            if (MoneyManagement != null)
            {
                MoneyManagement.PropertyChanged -= OnMoneyManagementPropertyChanged;
            }

            if (Simulation != null)
            {
                Simulation.TriggerRealPurchase -= OnTriggerRealPurchase;
            }

            if (_derivApiService != null)
            {
                _derivApiService.TickReceived -= OnTickReceived;
                _derivApiService.ProfitTableReceived -= OnProfitTableReceived;
                _derivApiService.OpenContractReceived -= OnOpenContractReceived;
            }

            // Dispose semaphore
            _timerSemaphore?.Dispose();

            // Dispose ViewModels if they implement IDisposable
            (AccountInfo as IDisposable)?.Dispose();
            (Purchase as IDisposable)?.Dispose();
            (Simulation as IDisposable)?.Dispose();

            GC.SuppressFinalize(this);
        }
    }
}

public class ContractPurchasedEventArgs : EventArgs
{
    public string ContractType { get; set; } = string.Empty;
    public string ContractId { get; set; } = string.Empty; // Será o proposal_id inicialmente, depois atualizado para transaction_id
    public decimal StartSpot { get; set; }
    public decimal Stake { get; set; }
    public decimal Payout { get; set; }
    public int Duration { get; set; }
    public string DurationType { get; set; } = string.Empty;
}