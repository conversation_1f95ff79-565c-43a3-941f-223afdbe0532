<UserControl x:Class="Excalibur.Views.ProposalView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="320">
    
    <UserControl.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="BorderBrush" Value="#FF404040"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Padding="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="BorderBrush" Value="#FF404040"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- TextBox para modo editável -->
                                <TextBox x:Name="PART_EditableTextBox"
                                         Grid.Column="0"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         Foreground="{TemplateBinding Foreground}"
                                         FontSize="{TemplateBinding FontSize}"
                                         Padding="{TemplateBinding Padding}"
                                         VerticalAlignment="Center"
                                         HorizontalAlignment="Stretch"
                                         CaretBrush="{TemplateBinding Foreground}"
                                         Visibility="Collapsed"/>
                                
                                <!-- ContentPresenter para modo não editável -->
                                <ContentPresenter x:Name="ContentSite"
                                                  Grid.Column="0"
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                  ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}"
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Left"
                                                  IsHitTestVisible="False"/>
                                
                                <ToggleButton Grid.Column="1"
                                              x:Name="ToggleButton"
                                              IsChecked="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                              Background="Transparent"
                                              BorderThickness="0"
                                              Focusable="False">
                                    <Path Data="M 0 0 L 4 4 L 8 0 Z"
                                          Fill="#FFCCCCCC"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                                </ToggleButton>
                            </Grid>
                            <Popup IsOpen="{TemplateBinding IsDropDownOpen}"
                                   Placement="Bottom"
                                   Focusable="False"
                                   AllowsTransparency="True"
                                   PopupAnimation="Slide">
                                <Border Background="#FF3F3F46"
                                        BorderBrush="#FF404040"
                                        BorderThickness="1"
                                        CornerRadius="4"
                                        MinWidth="{TemplateBinding ActualWidth}">
                                    <ScrollViewer MaxHeight="200">
                                        <ItemsPresenter/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsEditable" Value="True">
                                <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="ContentSite" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="ItemContainerStyle">
                <Setter.Value>
                    <Style TargetType="ComboBoxItem">
                        <Setter Property="Foreground" Value="#FFCCCCCC"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="Padding" Value="8,4"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBoxItem">
                                    <Border Background="{TemplateBinding Background}"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#FF404040"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#FF0078D4"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Setter.Value>
            </Setter>
        </Style>
        
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <DropShadowEffect x:Key="GreenShadowEffect" Color="#FF4CAF50" BlurRadius="55" ShadowDepth="0" Opacity="1"/>
        <DropShadowEffect x:Key="BlueShadowEffect" Color="#FF0078D4" BlurRadius="55" ShadowDepth="0" Opacity="1"/>
        
        <!-- Save Checkbox Style -->
        <Style x:Key="SaveCheckboxStyle" TargetType="CheckBox">
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="FontSize" Value="9"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="CheckboxBorder"
                                    Width="14" Height="14"
                                    Background="#FF3F3F46"
                                    BorderBrush="#FF666666"
                                    BorderThickness="1"
                                    CornerRadius="2"
                                    Margin="0,0,4,0">
                                <Path x:Name="CheckMark"
                                      Data="M 2 6 L 5 9 L 12 2"
                                      Stroke="#FF00C853"
                                      StrokeThickness="2"
                                      Visibility="Collapsed"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="{TemplateBinding Content}"
                                       Foreground="{TemplateBinding Foreground}"
                                       FontSize="{TemplateBinding FontSize}"
                                       FontWeight="{TemplateBinding FontWeight}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="CheckboxBorder" Property="Background" Value="#FF2E2E2E"/>
                                <Setter TargetName="CheckboxBorder" Property="BorderBrush" Value="#FF00C853"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="CheckboxBorder" Property="BorderBrush" Value="#FF888888"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Toggle Switch Style (mesmo usado em MoneyManagementView) -->
        <Style x:Key="ToggleSwitchStyle" TargetType="ToggleButton">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Grid>
                            <!-- Trilho -->
                            <Border x:Name="SwitchBorder"
                                    CornerRadius="9"
                                    Background="#FF5A5A5A"
                                    BorderBrush="#FF888888"
                                    BorderThickness="1"/>

                            <!-- Botão -->
                            <Ellipse x:Name="Knob"
                                     Width="16" Height="16"
                                     Fill="White"
                                     Margin="1"
                                     HorizontalAlignment="Left"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="SwitchBorder" Property="Background" Value="#FFFFC107"/>
                                <Setter TargetName="Knob" Property="HorizontalAlignment" Value="Right"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="SwitchBorder" Property="Opacity" Value="0.5"/>
                                <Setter TargetName="Knob" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Text="Proposta"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Foreground="#FFCCCCCC"
                           Margin="0,0,0,12"/>

                <TextBlock Grid.Column="1"
                           FontSize="12"
                           FontWeight="SemiBold"
                           Margin="0,0,0,12">
                    <Run Text="Spot:" Foreground="#FFCCCCCC"/>
                    <Run Text=" " />
                    <Run Text="{Binding CurrentSpot, StringFormat={}{0:F3}}" Foreground="#FFFFA500" FontSize="14" FontWeight="Bold"/>
                </TextBlock>
            </Grid>
            
            <!-- Descrição do Contrato -->
            <TextBlock Text="{Binding ContractDescription}"
                       FontSize="11"
                       FontWeight="Medium"
                       Foreground="#FF888888"
                       TextWrapping="Wrap"
                       Margin="0,0,0,16"/>
            
            <!-- Stake -->
            <TextBlock Text="Stake (USD)" Style="{StaticResource LabelStyle}"/>
            <TextBox Text="{Binding StakeText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource InputStyle}"/>
            
            <!-- Duration -->
            <TextBlock Text="Duration" Style="{StaticResource LabelStyle}"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="8"/>
                    <ColumnDefinition Width="80"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         Text="{Binding Duration, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource InputStyle}"/>
                
                <ComboBox Grid.Column="2"
                          SelectedValuePath="Tag"
                          SelectedValue="{Binding DurationType}"
                          Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="Ticks" Tag="t"/>
                    <ComboBoxItem Content="Seconds" Tag="s"/>
                    <ComboBoxItem Content="Minutes" Tag="m"/>
                    <ComboBoxItem Content="Hours" Tag="h"/>
                    <ComboBoxItem Content="Days" Tag="d"/>
                </ComboBox>
            </Grid>
            
            <!-- Barrier (condicional) -->
            <StackPanel Visibility="{Binding ShowBarrierField, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="Barrier" Style="{StaticResource LabelStyle}"/>
                <ComboBox ItemsSource="{Binding BarrierOptions}"
                          Text="{Binding BarrierText, UpdateSourceTrigger=PropertyChanged}"
                          SelectedItem="{Binding BarrierText, Mode=TwoWay}"
                          IsEditable="True"
                          Style="{StaticResource ComboBoxStyle}"/>
            </StackPanel>
            
            <!-- Digit (condicional) -->
            <StackPanel Visibility="{Binding ShowDigitField, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="Digit (0-9)" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Digit, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource InputStyle}"/>
            </StackPanel>
            
            <!-- Separator -->
            <Rectangle Height="1" 
                       Fill="#FF404040" 
                       Margin="0,16,0,12"/>
            
            <!-- Payout -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0"
                           Text="Payout:"
                           FontSize="13"
                           FontWeight="SemiBold"
                           Foreground="#FFCCCCCC"
                           VerticalAlignment="Center"/>
                
                <TextBlock Grid.Column="1"
                           Text="{Binding Payout, StringFormat='{}{0:C2}'}"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#FF4CAF50"
                           VerticalAlignment="Center"/>
            </Grid>
            
            <!-- Loading indicator (mantém altura fixa usando Hidden em vez de Collapsed) -->
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        Margin="0,8,0,0"
                        Height="18">
                <StackPanel.Style>
                    <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Hidden"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Style>
                <TextBlock Text="Calculating..."
                           FontSize="11"
                           FontStyle="Italic"
                           Foreground="#FF888888"/>
            </StackPanel>
            
            <!-- Action Buttons + Toggle -->
            <Grid Margin="0,0,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Botões -->
                <StackPanel Orientation="Horizontal" Grid.Column="0">
                    <!-- Buy Button -->
                    <Button Content="BUY"
                            Width="95"
                            Command="{Binding BuyCommand}"
                            IsEnabled="{Binding CanBuy}"
                            Margin="0,0,8,0"
                            Height="40"
                            FontSize="15"
                            FontWeight="Bold"
                            Background="#FF4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            UseLayoutRounding="True"
                            SnapsToDevicePixels="True">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border x:Name="PART_Border"
                                        Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Transparent" BlurRadius="0" ShadowDepth="0" Opacity="0"/>
                                    </Border.Effect>
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF45A049"/>
                                        <Setter Property="Effect" Value="{DynamicResource GreenShadowEffect}"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FF3E8E41"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Background" Value="#FF6C6C6C"/>
                                        <Setter Property="Foreground" Value="#FFA0A0A0"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Simulate/Stop Button -->
                    <Button Content="{Binding SimulateButtonText}"
                            Width="95"
                            Command="{Binding SimulateCommand}"
                            IsEnabled="{Binding CanBuy}"
                            Height="40"
                            FontSize="15"
                            FontWeight="Bold"
                            Background="{Binding SimulateButtonColor}"
                            Foreground="White"
                            BorderThickness="0"
                            UseLayoutRounding="True"
                            SnapsToDevicePixels="True">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border x:Name="PART_Border"
                                        Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Transparent" BlurRadius="0" ShadowDepth="0" Opacity="0"/>
                                    </Border.Effect>
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <DataTrigger Binding="{Binding IsAutoSimulating}" Value="False">
                                        <Setter Property="Background" Value="#FF0078D4"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsAutoSimulating}" Value="True">
                                        <Setter Property="Background" Value="#FFD32F2F"/>
                                    </DataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsAutoSimulating}" Value="False"/>
                                            <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsMouseOver}" Value="True"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FF006CBA"/>
                                        <Setter Property="Effect" Value="{DynamicResource BlueShadowEffect}"/>
                                    </MultiDataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsAutoSimulating}" Value="True"/>
                                            <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsMouseOver}" Value="True"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FFB71C1C"/>
                                        <Setter Property="Effect">
                                            <Setter.Value>
                                                <DropShadowEffect Color="#FFD32F2F" BlurRadius="55" ShadowDepth="0" Opacity="1"/>
                                            </Setter.Value>
                                        </Setter>
                                    </MultiDataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsAutoSimulating}" Value="False"/>
                                            <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsPressed}" Value="True"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FF0059A8"/>
                                    </MultiDataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding IsAutoSimulating}" Value="True"/>
                                            <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsPressed}" Value="True"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Background" Value="#FF8E0000"/>
                                    </MultiDataTrigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Background" Value="#FF6C6C6C"/>
                                        <Setter Property="Foreground" Value="#FFA0A0A0"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </StackPanel>

                <!-- Toggle Contínuo -->
                <StackPanel Orientation="Horizontal" Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                    <TextBlock Text="Manter" Foreground="#FFCCCCCC" FontSize="11" VerticalAlignment="Center" Margin="0,0,6,0"/>
                    <ToggleButton IsChecked="{Binding IsContinuous}" Style="{StaticResource ToggleSwitchStyle}"/>
                </StackPanel>
            </Grid>

            <!-- Result Analysis Inline Fields -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,4,0,0">
                <!-- Amostra -->
                <StackPanel Margin="0,0,12,0">
                    <TextBlock Text="Amostra" Style="{StaticResource LabelStyle}"/>
                    <TextBox Width="65"
                             Text="{Binding DataContext.MoneyManagement.AmostraText, RelativeSource={RelativeSource AncestorType=Window}, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource InputStyle}"/>
                </StackPanel>

                <!-- Win -->
                <StackPanel Margin="0,0,12,0">
                    <TextBlock Text="Win" Style="{StaticResource LabelStyle}"/>
                    <TextBox Width="65"
                             Text="{Binding DataContext.MoneyManagement.WinText, RelativeSource={RelativeSource AncestorType=Window}, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource InputStyle}"/>
                </StackPanel>

                <!-- Loss + Save Checkbox -->
                <StackPanel>
                    <TextBlock Text="Loss" Style="{StaticResource LabelStyle}"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="65"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                                 Text="{Binding DataContext.MoneyManagement.LossText, RelativeSource={RelativeSource AncestorType=Window}, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputStyle}"/>
                        
                        <CheckBox Grid.Column="2"
                                  HorizontalAlignment="Right"
                                  VerticalAlignment="Center"
                                  IsChecked="{Binding SaveSettings}"
                                  Content="Save"
                                  Style="{StaticResource SaveCheckboxStyle}"/>
                    </Grid>
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>